<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, button {
            padding: 10px;
            font-size: 16px;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔐 Login API Test</h1>
    <p>This page tests the login API directly to help debug login issues.</p>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="credential">Username:</label>
            <input type="text" id="credential" value="admin" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="admin" required>
        </div>
        
        <button type="submit">Test Login</button>
    </form>
    
    <div id="result"></div>
    
    <h2>📋 Instructions:</h2>
    <ol>
        <li>Make sure the server is running on localhost:3000</li>
        <li>Click "Test Login" to test the API</li>
        <li>Check the browser console (F12) for detailed logs</li>
        <li>If successful, try the main login page</li>
    </ol>
    
    <h2>🔗 Quick Links:</h2>
    <ul>
        <li><a href="/api/test" target="_blank">Test API Status</a></li>
        <li><a href="/frontend/index.html">Main Login Page</a></li>
        <li><a href="/frontend/dashboard.html">Dashboard (after login)</a></li>
    </ul>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const credential = document.getElementById('credential').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            console.log('=== DIRECT API TEST ===');
            console.log('Testing login with:', { credential, password: password ? '[PROVIDED]' : '[MISSING]' });
            
            try {
                resultDiv.innerHTML = '<p>Testing login...</p>';
                
                const response = await fetch('/api/auth/login-basic', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        credential,
                        password
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>User:</strong> ${data.user.first_name} ${data.user.last_name}</p>
                            <p><strong>Role:</strong> ${data.user.role}</p>
                            <p><strong>Token:</strong> ${data.accessToken.substring(0, 20)}...</p>
                            <p>The API is working correctly. You can now try the main login page.</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Login Failed</h3>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                            <p><strong>Status:</strong> ${response.status}</p>
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('Login test error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Check if the server is running and try again.</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
