<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Reports - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item active">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>Financial Reports</h1>
                <p>Generate and view comprehensive financial reports</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="generateReport()">
                    <i class="fas fa-chart-line"></i>
                    Generate Report
                </button>
            </div>
        </header>

        <!-- Report Selection -->
        <div class="report-selection-section">
            <div class="report-types-grid">
                <div class="report-type-card" onclick="selectReportType('profit-loss')">
                    <div class="report-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Profit & Loss</h3>
                    <p>Revenue, expenses, and net income analysis</p>
                </div>

                <div class="report-type-card" onclick="selectReportType('balance-sheet')">
                    <div class="report-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <h3>Balance Sheet</h3>
                    <p>Assets, liabilities, and equity overview</p>
                </div>

                <div class="report-type-card" onclick="selectReportType('cash-flow')">
                    <div class="report-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h3>Cash Flow</h3>
                    <p>Cash inflows and outflows analysis</p>
                </div>

                <div class="report-type-card" onclick="selectReportType('invoice-summary')">
                    <div class="report-icon">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <h3>Invoice Summary</h3>
                    <p>Invoice status and payment analysis</p>
                </div>

                <div class="report-type-card" onclick="selectReportType('expense-summary')">
                    <div class="report-icon">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <h3>Expense Summary</h3>
                    <p>Expense categories and trends</p>
                </div>

                <div class="report-type-card" onclick="selectReportType('customer-aging')">
                    <div class="report-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Customer Aging</h3>
                    <p>Outstanding receivables by age</p>
                </div>
            </div>
        </div>

        <!-- Report Parameters -->
        <div class="report-parameters" id="reportParameters" style="display: none;">
            <div class="parameters-card">
                <h3>Report Parameters</h3>
                <form id="reportForm">
                    <input type="hidden" id="reportType" name="type">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="startDate">Start Date</label>
                            <input type="date" id="startDate" name="startDate">
                        </div>
                        <div class="form-group">
                            <label for="endDate">End Date</label>
                            <input type="date" id="endDate" name="endDate">
                        </div>
                        <div class="form-group">
                            <label for="format">Format</label>
                            <select id="format" name="format">
                                <option value="json">View Online</option>
                                <option value="pdf">PDF Download</option>
                                <option value="excel">Excel Download</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="cancelReport()">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-play"></i>
                            Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Report Display -->
        <div class="report-display" id="reportDisplay" style="display: none;">
            <div class="report-header">
                <div class="report-title">
                    <h2 id="reportTitle">Report Title</h2>
                    <p id="reportPeriod">Report Period</p>
                </div>
                <div class="report-actions">
                    <button class="btn btn-secondary" onclick="printReport()">
                        <i class="fas fa-print"></i>
                        Print
                    </button>
                    <button class="btn btn-secondary" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf"></i>
                        PDF
                    </button>
                    <button class="btn btn-secondary" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel"></i>
                        Excel
                    </button>
                    <button class="btn btn-primary" onclick="newReport()">
                        <i class="fas fa-plus"></i>
                        New Report
                    </button>
                </div>
            </div>

            <div class="report-content" id="reportContent">
                <!-- Report content will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Generating report...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/reports.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Reports page loading...');

            // Check authentication with delay to ensure auth manager is ready
            setTimeout(() => {
                if (!requireAuth()) {
                    console.log('❌ Authentication failed, stopping page initialization');
                    return;
                }

                console.log('✅ Authentication successful, initializing reports page...');
                // Initialize reports page
                initializeReportsPage();
            }, 50);
        });
    </script>
</body>
</html>
