@echo off
echo ========================================
echo   FIXING LOGIN REDIRECT ISSUE
echo ========================================
echo.

echo [1/4] Stopping any running servers...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [2/4] Syncing updated frontend files...
xcopy frontend\js\auth.js public\frontend\js\ /Y >nul
xcopy frontend\js\dashboard.js public\frontend\js\ /Y >nul

echo [3/4] Clearing browser cache data...
echo NOTE: You may need to manually clear browser cache (Ctrl+F5)

echo [4/4] Starting server with debug tools...
echo.
echo ========================================
echo   LOGIN REDIRECT ISSUE FIXED!
echo ========================================
echo.
echo WHAT WAS FIXED:
echo - ✅ Enhanced authentication checking
echo - ✅ Added localStorage refresh logic
echo - ✅ Improved debugging for auth flow
echo - ✅ Fixed API endpoint calls
echo.
echo AVAILABLE DEBUG TOOLS:
echo - Main Login: http://localhost:3000/frontend/index.html
echo - Debug Tool: http://localhost:3000/debug-auth.html
echo - Test Login: http://localhost:3000/test-login.html
echo.
echo LOGIN FLOW:
echo 1. Login with admin/admin
echo 2. Check console for authentication logs
echo 3. Should stay on dashboard after redirect
echo.
echo If still redirecting to login:
echo - Use debug tool to check auth status
echo - Clear browser cache (Ctrl+F5)
echo - Check console logs for errors
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

npm run dev
