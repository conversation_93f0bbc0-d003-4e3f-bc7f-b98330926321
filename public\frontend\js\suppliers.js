// Supplier Management JavaScript
class SupplierManager {
    constructor() {
        this.suppliers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.currentSupplier = null;
    }

    // Initialize suppliers page
    async initialize() {
        try {
            this.showLoading();
            await this.loadSuppliers();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize suppliers page:', error);
            this.hideLoading();
            this.showAlert('Failed to load suppliers data', 'error');
        }
    }

    // Load suppliers
    async loadSuppliers() {
        try {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: searchTerm,
                status: statusFilter
            });

            const response = await apiClient.request(`/suppliers?${params}`);
            this.suppliers = response.data.suppliers;
            this.updatePagination(response.data.pagination);
            this.renderSuppliersTable();
        } catch (error) {
            console.error('Failed to load suppliers:', error);
            this.showAlert('Failed to load suppliers', 'error');
        }
    }

    // Render suppliers table
    renderSuppliersTable() {
        const tbody = document.getElementById('suppliersTableBody');
        tbody.innerHTML = '';

        if (this.suppliers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No suppliers found</td>
                </tr>
            `;
            return;
        }

        this.suppliers.forEach(supplier => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <a href="#" onclick="viewSupplier(${supplier.id})" class="supplier-link">
                        ${supplier.supplier_code}
                    </a>
                </td>
                <td>${supplier.name}</td>
                <td>${supplier.email || '-'}</td>
                <td>${supplier.phone || '-'}</td>
                <td>${supplier.total_expenses || 0}</td>
                <td>${formatUtils.currency(supplier.total_spent || 0)}</td>
                <td>
                    <span class="status-badge status-${supplier.is_active ? 'active' : 'inactive'}">
                        ${supplier.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="editSupplier(${supplier.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="createExpenseForSupplier(${supplier.id})" title="Create Expense">
                            <i class="fas fa-receipt"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="viewSupplier(${supplier.id})" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteSupplier(${supplier.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Update pagination
    updatePagination(pagination) {
        this.currentPage = pagination.page;
        this.totalPages = pagination.pages;

        // Update pagination info
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        document.getElementById('paginationInfo').textContent = 
            `Showing ${start}-${end} of ${pagination.total} suppliers`;

        // Update pagination controls
        document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
        document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;

        // Update page numbers
        this.renderPageNumbers();
    }

    // Render page numbers
    renderPageNumbers() {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }

    // Go to specific page
    async goToPage(page) {
        this.currentPage = page;
        await this.loadSuppliers();
    }

    // Change page (next/previous)
    async changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1 && newPage <= this.totalPages) {
            await this.goToPage(newPage);
        }
    }

    // Show create supplier modal
    showCreateSupplierModal() {
        this.currentSupplier = null;
        document.getElementById('modalTitle').textContent = 'Add New Supplier';
        document.getElementById('supplierForm').reset();
        document.getElementById('statusGroup').style.display = 'none';
        document.getElementById('supplierModal').style.display = 'flex';
    }

    // Show edit supplier modal
    async showEditSupplierModal(supplierId) {
        try {
            this.showLoading();
            const response = await apiClient.request(`/suppliers/${supplierId}`);
            this.currentSupplier = response.data.supplier;
            
            document.getElementById('modalTitle').textContent = 'Edit Supplier';
            this.populateSupplierForm(this.currentSupplier);
            document.getElementById('statusGroup').style.display = 'block';
            document.getElementById('supplierModal').style.display = 'flex';
            
            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to load supplier details', 'error');
        }
    }

    // Populate supplier form
    populateSupplierForm(supplier) {
        document.getElementById('supplierName').value = supplier.name || '';
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('taxNumber').value = supplier.tax_number || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierCity').value = supplier.city || '';
        document.getElementById('supplierCountry').value = supplier.country || '';
        document.getElementById('paymentTerms').value = supplier.payment_terms || 30;
        document.getElementById('supplierStatus').value = supplier.is_active ? '1' : '0';
    }

    // Close supplier modal
    closeSupplierModal() {
        document.getElementById('supplierModal').style.display = 'none';
        this.currentSupplier = null;
    }

    // Save supplier
    async saveSupplier() {
        try {
            this.showLoading();
            
            // Collect form data
            const formData = new FormData(document.getElementById('supplierForm'));
            const supplierData = Object.fromEntries(formData.entries());
            
            // Convert numeric fields
            supplierData.payment_terms = parseInt(supplierData.payment_terms) || 30;
            supplierData.is_active = supplierData.is_active === '1';
            
            // Validate required fields
            if (!supplierData.name) {
                throw new Error('Supplier name is required');
            }
            
            // Save supplier
            const endpoint = this.currentSupplier ? `/suppliers/${this.currentSupplier.id}` : '/suppliers';
            const method = this.currentSupplier ? 'PUT' : 'POST';
            
            const response = await apiClient.request(endpoint, {
                method,
                body: JSON.stringify(supplierData)
            });
            
            this.hideLoading();
            this.showAlert('Supplier saved successfully!', 'success');
            this.closeSupplierModal();
            await this.loadSuppliers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save supplier', 'error');
        }
    }

    // Delete supplier
    async deleteSupplier(supplierId) {
        if (!confirm('Are you sure you want to delete this supplier? This action cannot be undone.')) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/suppliers/${supplierId}`, { method: 'DELETE' });
            
            this.hideLoading();
            this.showAlert('Supplier deleted successfully!', 'success');
            await this.loadSuppliers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to delete supplier', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        document.getElementById('searchInput').addEventListener('input', 
            debounce(() => this.loadSuppliers(), 300));
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Global supplier manager instance
const supplierManager = new SupplierManager();

// Global functions for HTML onclick handlers
function initializeSuppliersPage() {
    supplierManager.initialize();
}

function showCreateSupplierModal() {
    supplierManager.showCreateSupplierModal();
}

function closeSupplierModal() {
    supplierManager.closeSupplierModal();
}

function saveSupplier() {
    supplierManager.saveSupplier();
}

function filterSuppliers() {
    supplierManager.currentPage = 1;
    supplierManager.loadSuppliers();
}

function changePage(direction) {
    supplierManager.changePage(direction);
}

function editSupplier(id) {
    supplierManager.showEditSupplierModal(id);
}

function viewSupplier(id) {
    console.log('View supplier:', id);
}

function deleteSupplier(id) {
    supplierManager.deleteSupplier(id);
}

function createExpenseForSupplier(supplierId) {
    // Redirect to expense creation with pre-selected supplier
    window.location.href = `expenses.html?action=create&supplier=${supplierId}`;
}

function exportSuppliers() {
    console.log('Export suppliers');
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
