<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Accounting - Login</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-calculator"></i>
                    <h1>Enterprise Accounting</h1>
                </div>
                <p class="subtitle">Secure Financial Management System</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="credential">
                        <i class="fas fa-user"></i>
                        Username or Email
                    </label>
                    <input
                        type="text"
                        id="credential"
                        name="credential"
                        required
                        autocomplete="username"
                        placeholder="Default: admin"
                        value="admin"
                    >
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <div class="password-input">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            autocomplete="current-password"
                            placeholder="Default: admin"
                            value="admin"
                        >
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- MFA Token Field (hidden by default) -->
                <div class="form-group" id="mfaGroup" style="display: none;">
                    <label for="mfaToken">
                        <i class="fas fa-shield-alt"></i>
                        MFA Token
                    </label>
                    <input 
                        type="text" 
                        id="mfaToken" 
                        name="mfaToken" 
                        placeholder="Enter 6-digit MFA code"
                        maxlength="6"
                        pattern="[0-9]{6}"
                    >
                    <small class="help-text">Enter the 6-digit code from your authenticator app</small>
                </div>

                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot Password?</a>
                </div>

                <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>

                <div class="divider">
                    <span>or</span>
                </div>

                <!-- Signup button removed - only available to logged-in admins -->
            </form>

            <!-- Registration Form (hidden by default) -->
            <form id="registerForm" class="register-form" style="display: none;">
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">
                            <i class="fas fa-user"></i>
                            First Name
                        </label>
                        <input type="text" id="firstName" name="firstName" required placeholder="First Name">
                    </div>
                    <div class="form-group">
                        <label for="lastName">
                            <i class="fas fa-user"></i>
                            Last Name
                        </label>
                        <input type="text" id="lastName" name="lastName" required placeholder="Last Name">
                    </div>
                </div>

                <div class="form-group">
                    <label for="regUsername">
                        <i class="fas fa-at"></i>
                        Username
                    </label>
                    <input type="text" id="regUsername" name="username" required placeholder="Choose a username">
                </div>

                <div class="form-group">
                    <label for="regEmail">
                        <i class="fas fa-envelope"></i>
                        Email
                    </label>
                    <input type="email" id="regEmail" name="email" required placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="phone">
                        <i class="fas fa-phone"></i>
                        Phone (Optional)
                    </label>
                    <input type="tel" id="phone" name="phone" placeholder="+92 300 1234567">
                </div>

                <div class="form-group">
                    <label for="role">
                        <i class="fas fa-user-tag"></i>
                        Role
                    </label>
                    <select id="role" name="role" required>
                        <option value="employee">Employee</option>
                        <option value="accountant">Accountant</option>
                        <option value="manager">Manager</option>
                        <option value="auditor">Auditor</option>
                        <option value="admin">Administrator</option>
                    </select>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="regPassword">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <input type="password" id="regPassword" name="password" required placeholder="Create password">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">
                            <i class="fas fa-lock"></i>
                            Confirm Password
                        </label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Confirm password">
                    </div>
                </div>

                <div class="password-requirements">
                    <small>Enter a secure password</small>
                </div>

                <button type="submit" class="btn btn-primary" id="registerBtn">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </button>

                <button type="button" class="btn btn-secondary" onclick="showLoginForm()">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </button>
            </form>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loadingSpinner" style="display: none;">
                <div class="spinner"></div>
                <p>Please wait...</p>
            </div>

            <!-- Alert Messages -->
            <div class="alert" id="alertMessage" style="display: none;">
                <span class="alert-text"></span>
                <button class="alert-close" onclick="hideAlert()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="login-footer">
            <div class="security-features">
                <div class="feature">
                    <i class="fas fa-shield-alt"></i>
                    <span>256-bit Encryption</span>
                </div>
                <div class="feature">
                    <i class="fas fa-lock"></i>
                    <span>Multi-Factor Authentication</span>
                </div>
                <div class="feature">
                    <i class="fas fa-eye"></i>
                    <span>Audit Trail</span>
                </div>
            </div>
            <p class="copyright">© 2024 Enterprise Accounting Software. All rights reserved.</p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Initialize login page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in with proper validation
            const accessToken = localStorage.getItem('accessToken');
            const currentUser = localStorage.getItem('currentUser');

            if (accessToken && currentUser) {
                try {
                    const user = JSON.parse(currentUser);
                    if (user && user.id) {
                        console.log('🔄 User already logged in, redirecting to dashboard...');
                        window.location.href = 'dashboard.html';
                        return;
                    }
                } catch (e) {
                    console.log('🗑️ Invalid user data in localStorage, clearing...');
                    localStorage.removeItem('accessToken');
                    localStorage.removeItem('currentUser');
                }
            }

            // Initialize form handlers
            initializeLoginForm();
            initializeRegisterForm();
        });

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        function showRegisterForm() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
            document.querySelector('.login-header .subtitle').textContent = 'Create Your Account';
        }

        function showLoginForm() {
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
            document.querySelector('.login-header .subtitle').textContent = 'Secure Financial Management System';
        }

        function hideAlert() {
            document.getElementById('alertMessage').style.display = 'none';
        }
    </script>
</body>
</html>
