// Reports Management JavaScript
class ReportsManager {
    constructor() {
        this.currentReportType = null;
        this.currentReportData = null;
    }

    // Initialize reports page
    async initialize() {
        try {
            this.setupEventListeners();
            this.setDefaultDates();
        } catch (error) {
            console.error('Failed to initialize reports page:', error);
            this.showAlert('Failed to initialize reports page', 'error');
        }
    }

    // Set default dates (current month)
    setDefaultDates() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
        document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];
    }

    // Select report type
    selectReportType(reportType) {
        this.currentReportType = reportType;
        document.getElementById('reportType').value = reportType;

        // Update UI
        document.querySelectorAll('.report-type-card').forEach(card => {
            card.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');

        // Show parameters section
        document.getElementById('reportParameters').style.display = 'block';
        document.getElementById('reportDisplay').style.display = 'none';

        // Adjust parameters based on report type
        this.adjustParametersForReportType(reportType);
    }

    // Adjust parameters based on report type
    adjustParametersForReportType(reportType) {
        const startDateGroup = document.getElementById('startDate').closest('.form-group');
        const endDateGroup = document.getElementById('endDate').closest('.form-group');

        if (reportType === 'balance-sheet' || reportType === 'customer-aging') {
            // These reports only need end date (as of date)
            startDateGroup.style.display = 'none';
            endDateGroup.querySelector('label').textContent = 'As of Date';
        } else {
            // Other reports need date range
            startDateGroup.style.display = 'block';
            endDateGroup.querySelector('label').textContent = 'End Date';
        }
    }

    // Generate report
    async generateReport() {
        try {
            if (!this.currentReportType) {
                this.showAlert('Please select a report type', 'warning');
                return;
            }

            this.showLoading();

            const formData = new FormData(document.getElementById('reportForm'));
            const params = new URLSearchParams();

            for (const [key, value] of formData.entries()) {
                if (value) params.append(key, value);
            }

            const response = await apiClient.request(`/reports?${params}`);
            this.currentReportData = response.data;

            this.hideLoading();
            this.displayReport(this.currentReportData);

        } catch (error) {
            this.hideLoading();
            console.error('Failed to generate report:', error);
            this.showAlert('Failed to generate report', 'error');
        }
    }

    // Display report
    displayReport(reportData) {
        document.getElementById('reportTitle').textContent = reportData.reportType;
        
        // Set period text
        let periodText = '';
        if (reportData.period) {
            if (reportData.period.startDate && reportData.period.endDate) {
                periodText = `${formatUtils.date(reportData.period.startDate)} - ${formatUtils.date(reportData.period.endDate)}`;
            } else if (reportData.asOfDate) {
                periodText = `As of ${formatUtils.date(reportData.asOfDate)}`;
            }
        }
        document.getElementById('reportPeriod').textContent = periodText;

        // Generate report content based on type
        const content = this.generateReportContent(reportData);
        document.getElementById('reportContent').innerHTML = content;

        // Show report display
        document.getElementById('reportParameters').style.display = 'none';
        document.getElementById('reportDisplay').style.display = 'block';
    }

    // Generate report content HTML
    generateReportContent(reportData) {
        switch (this.currentReportType) {
            case 'profit-loss':
                return this.generateProfitLossContent(reportData);
            case 'balance-sheet':
                return this.generateBalanceSheetContent(reportData);
            case 'cash-flow':
                return this.generateCashFlowContent(reportData);
            case 'invoice-summary':
                return this.generateInvoiceSummaryContent(reportData);
            case 'expense-summary':
                return this.generateExpenseSummaryContent(reportData);
            case 'customer-aging':
                return this.generateCustomerAgingContent(reportData);
            default:
                return '<p>Report type not supported</p>';
        }
    }

    // Generate Profit & Loss content
    generateProfitLossContent(data) {
        return `
            <div class="financial-report">
                <div class="report-section">
                    <h3>Revenue</h3>
                    <table class="report-table">
                        <tbody>
                            ${data.revenue.items.map(item => `
                                <tr>
                                    <td>${item.account_name}</td>
                                    <td class="amount">${formatUtils.currency(item.amount)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>Total Revenue</strong></td>
                                <td class="amount"><strong>${formatUtils.currency(data.revenue.total)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <h3>Expenses</h3>
                    <table class="report-table">
                        <tbody>
                            ${data.expenses.items.map(item => `
                                <tr>
                                    <td>${item.account_name}</td>
                                    <td class="amount">${formatUtils.currency(item.amount)}</td>
                                </tr>
                            `).join('')}
                            <tr class="total-row">
                                <td><strong>Total Expenses</strong></td>
                                <td class="amount"><strong>${formatUtils.currency(data.expenses.total)}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <table class="report-table">
                        <tbody>
                            <tr class="net-income-row">
                                <td><strong>Net Income</strong></td>
                                <td class="amount ${data.netIncome >= 0 ? 'positive' : 'negative'}">
                                    <strong>${formatUtils.currency(data.netIncome)}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Generate Balance Sheet content
    generateBalanceSheetContent(data) {
        return `
            <div class="financial-report">
                <div class="balance-sheet-grid">
                    <div class="assets-section">
                        <h3>Assets</h3>
                        <table class="report-table">
                            <tbody>
                                ${data.assets.items.map(item => `
                                    <tr>
                                        <td>${item.account_name}</td>
                                        <td class="amount">${formatUtils.currency(item.amount)}</td>
                                    </tr>
                                `).join('')}
                                <tr class="total-row">
                                    <td><strong>Total Assets</strong></td>
                                    <td class="amount"><strong>${formatUtils.currency(data.assets.total)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="liabilities-equity-section">
                        <div class="liabilities-section">
                            <h3>Liabilities</h3>
                            <table class="report-table">
                                <tbody>
                                    ${data.liabilities.items.map(item => `
                                        <tr>
                                            <td>${item.account_name}</td>
                                            <td class="amount">${formatUtils.currency(item.amount)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>Total Liabilities</strong></td>
                                        <td class="amount"><strong>${formatUtils.currency(data.liabilities.total)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="equity-section">
                            <h3>Equity</h3>
                            <table class="report-table">
                                <tbody>
                                    ${data.equity.items.map(item => `
                                        <tr>
                                            <td>${item.account_name}</td>
                                            <td class="amount">${formatUtils.currency(item.amount)}</td>
                                        </tr>
                                    `).join('')}
                                    <tr class="total-row">
                                        <td><strong>Total Equity</strong></td>
                                        <td class="amount"><strong>${formatUtils.currency(data.equity.total)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="total-section">
                            <table class="report-table">
                                <tbody>
                                    <tr class="grand-total-row">
                                        <td><strong>Total Liabilities & Equity</strong></td>
                                        <td class="amount"><strong>${formatUtils.currency(data.totalLiabilitiesAndEquity)}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Generate Cash Flow content
    generateCashFlowContent(data) {
        return `
            <div class="summary-report">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>Total Inflow</h4>
                        <div class="summary-value">${formatUtils.currency(data.summary.totalInflow)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>Total Outflow</h4>
                        <div class="summary-value">${formatUtils.currency(data.summary.totalOutflow)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>Net Cash Flow</h4>
                        <div class="summary-value ${data.summary.netCashFlow >= 0 ? 'positive' : 'negative'}">
                            ${formatUtils.currency(data.summary.netCashFlow)}
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>Cash Flow Transactions</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th>Account</th>
                                <th>Inflow</th>
                                <th>Outflow</th>
                                <th>Net Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.transactions && data.transactions.length > 0 ?
                                data.transactions.map(transaction => `
                                    <tr>
                                        <td>${formatUtils.date(transaction.transaction_date)}</td>
                                        <td>${transaction.description}</td>
                                        <td>${transaction.account_name}</td>
                                        <td class="amount">${formatUtils.currency(transaction.credit_amount)}</td>
                                        <td class="amount">${formatUtils.currency(transaction.debit_amount)}</td>
                                        <td class="amount ${transaction.net_amount >= 0 ? 'positive' : 'negative'}">
                                            ${formatUtils.currency(transaction.net_amount)}
                                        </td>
                                    </tr>
                                `).join('') :
                                '<tr><td colspan="6" class="text-center">No transactions found</td></tr>'
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Generate Invoice Summary content
    generateInvoiceSummaryContent(data) {
        return `
            <div class="summary-report">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>Total Invoices</h4>
                        <div class="summary-value">${data.summary.totalInvoices}</div>
                    </div>
                    <div class="summary-card">
                        <h4>Total Amount</h4>
                        <div class="summary-value">${formatUtils.currency(data.summary.totalAmount)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>Total Paid</h4>
                        <div class="summary-value">${formatUtils.currency(data.summary.totalPaid)}</div>
                    </div>
                    <div class="summary-card">
                        <h4>Outstanding</h4>
                        <div class="summary-value">${formatUtils.currency(data.summary.totalOutstanding)}</div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>By Status</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Count</th>
                                <th>Total Amount</th>
                                <th>Paid Amount</th>
                                <th>Outstanding</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.byStatus.map(item => `
                                <tr>
                                    <td>
                                        <span class="status-badge status-${item.status}">
                                            ${item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                        </span>
                                    </td>
                                    <td>${item.count}</td>
                                    <td class="amount">${formatUtils.currency(item.total_amount)}</td>
                                    <td class="amount">${formatUtils.currency(item.paid_amount)}</td>
                                    <td class="amount">${formatUtils.currency(item.outstanding_amount)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Generate Expense Summary content
    generateExpenseSummaryContent(data) {
        return `
            <div class="summary-report">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>Total Expenses</h4>
                        <div class="summary-value">${formatUtils.currency(data.totalExpenses)}</div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>By Category</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Count</th>
                                <th>Total Amount</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.byCategory.map(item => {
                                const percentage = data.totalExpenses > 0 ? (item.total_amount / data.totalExpenses) * 100 : 0;
                                return `
                                    <tr>
                                        <td>${item.category}</td>
                                        <td>${item.count}</td>
                                        <td class="amount">${formatUtils.currency(item.total_amount)}</td>
                                        <td>${percentage.toFixed(1)}%</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Generate Customer Aging content
    generateCustomerAgingContent(data) {
        const bucketOrder = ['Current', '1-30 days', '31-60 days', '61-90 days', '90+ days'];
        
        return `
            <div class="aging-report">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>Total Outstanding</h4>
                        <div class="summary-value">${formatUtils.currency(data.totalOutstanding)}</div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>Aging Summary</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>Aging Period</th>
                                <th>Count</th>
                                <th>Amount</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${bucketOrder.map(bucket => {
                                const bucketData = data.buckets[bucket] || { count: 0, amount: 0 };
                                const percentage = data.totalOutstanding > 0 ? (bucketData.amount / data.totalOutstanding) * 100 : 0;
                                return `
                                    <tr>
                                        <td>${bucket}</td>
                                        <td>${bucketData.count}</td>
                                        <td class="amount">${formatUtils.currency(bucketData.amount)}</td>
                                        <td>${percentage.toFixed(1)}%</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Cancel report
    cancelReport() {
        document.getElementById('reportParameters').style.display = 'none';
        document.getElementById('reportDisplay').style.display = 'none';
        document.querySelectorAll('.report-type-card').forEach(card => {
            card.classList.remove('selected');
        });
        this.currentReportType = null;
    }

    // New report
    newReport() {
        this.cancelReport();
    }

    // Print report
    printReport() {
        window.print();
    }

    // Export report
    async exportReport(format) {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('reportForm'));
            formData.set('format', format);
            
            const params = new URLSearchParams();
            for (const [key, value] of formData.entries()) {
                if (value) params.append(key, value);
            }

            // For now, just show a message
            this.hideLoading();
            this.showAlert(`Export to ${format.toUpperCase()} functionality will be implemented`, 'info');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to export report', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Any additional event listeners can be added here
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // You can implement a toast notification system here
    }
}

// Global reports manager instance
const reportsManager = new ReportsManager();

// Global functions for HTML onclick handlers
function initializeReportsPage() {
    reportsManager.initialize();
}

function selectReportType(reportType) {
    reportsManager.selectReportType(reportType);
}

function generateReport() {
    reportsManager.generateReport();
}

function cancelReport() {
    reportsManager.cancelReport();
}

function newReport() {
    reportsManager.newReport();
}

function printReport() {
    reportsManager.printReport();
}

function exportReport(format) {
    reportsManager.exportReport(format);
}
