# 🚀 Enterprise Accounting Software - Startup Guide

## Quick Fix for Internal Server Error

The internal server error was likely caused by database connection issues. I've created simplified APIs that work without a database connection to get you started immediately.

## 🔧 Step-by-Step Instructions

### 1. Start the Server
```bash
quick-start.bat
```

### 2. Wait for Server to Start
Look for this message in the terminal:
```
ready - started server on 0.0.0.0:3000
```

### 3. Access the Application
Open your browser and go to:
```
http://localhost:3000
```

You'll be automatically redirected to the login page.

### 4. Login with Demo Credentials

**Admin User:**
- Username: `admin`
- Password: `admin`

**Manager User:**
- Username: `manager`
- Password: `manager`

**Accountant User:**
- Username: `accountant`
- Password: `accountant`

### 5. Test the Application

After logging in, you should be able to:
- ✅ View the dashboard with demo financial data
- ✅ Navigate between all pages
- ✅ See role-based access (admin sees all features)

## 🔍 Troubleshooting

### If you still get "Internal Server Error":

1. **Check the terminal output** - Look for specific error messages
2. **Test the API directly** - Go to: `http://localhost:3000/api/test`
3. **Clear browser cache** - Press Ctrl+F5
4. **Try incognito mode** - To rule out browser cache issues

### If you get 404 errors:

1. **Make sure you're using the correct URL**: `http://localhost:3000`
2. **Check if files are accessible**: `http://localhost:3000/frontend/index.html`
3. **Run the sync script**: `sync-frontend.bat`

## 📱 Available Pages

Once logged in, you can access:

- **Dashboard**: `/frontend/dashboard.html`
- **Invoices**: `/frontend/invoices.html`
- **Customers**: `/frontend/customers.html`
- **Suppliers**: `/frontend/suppliers.html`
- **Expenses**: `/frontend/expenses.html`
- **Reports**: `/frontend/reports.html`
- **Users** (Admin only): `/frontend/users.html`
- **Settings** (Admin only): `/frontend/settings.html`

## 🎯 What's Working Now

- ✅ **Authentication**: Login with demo users
- ✅ **Dashboard**: Shows demo financial metrics
- ✅ **Navigation**: All pages are accessible
- ✅ **Role-based Access**: Different permissions for different users
- ✅ **Responsive Design**: Works on desktop and mobile

## 🔄 Next Steps (Optional)

Once the basic application is working, you can:

1. **Set up the database** using `setup-database.bat`
2. **Switch to full APIs** that use the database
3. **Add real data** instead of demo data
4. **Configure email settings** for notifications

## 📞 If You Need Help

If you're still experiencing issues, please share:

1. **What URL you're trying to access**
2. **What you see in the browser**
3. **Any error messages in the terminal**
4. **Any error messages in browser console** (F12 → Console)

The simplified version should work immediately without any database setup!
