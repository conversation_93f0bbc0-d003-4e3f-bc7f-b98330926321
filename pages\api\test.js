// Simple test API endpoint to verify the server is working
export default function handler(req, res) {
  if (req.method === 'GET') {
    res.status(200).json({
      success: true,
      message: '🎉 Enterprise Accounting API is working!',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      status: 'Server is running correctly',
      availableEndpoints: [
        'GET /api/test',
        'POST /api/auth/login-simple',
        'GET /api/dashboard/metrics-simple'
      ]
    });
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
