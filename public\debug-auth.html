<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover { background-color: #0056b3; }
        .clear-btn { background-color: #dc3545; }
        .clear-btn:hover { background-color: #c82333; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Authentication Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Current Authentication Status</h2>
        <div id="authStatus"></div>
        <button onclick="checkAuth()">Refresh Status</button>
        <button onclick="clearAuth()" class="clear-btn">Clear Auth Data</button>
    </div>
    
    <div class="debug-section">
        <h2>LocalStorage Data</h2>
        <div id="storageData"></div>
    </div>
    
    <div class="debug-section">
        <h2>Quick Actions</h2>
        <button onclick="goToLogin()">Go to Login</button>
        <button onclick="goToDashboard()">Go to Dashboard</button>
        <button onclick="testLogin()">Test Login (admin/admin)</button>
    </div>
    
    <div class="debug-section">
        <h2>Console Logs</h2>
        <div id="consoleLogs"></div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script src="frontend/js/auth.js"></script>
    <script>
        let logs = [];
        
        // Override console.log to capture logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            logs.push(args.join(' '));
            updateLogs();
        };
        
        function updateLogs() {
            const logsDiv = document.getElementById('consoleLogs');
            logsDiv.innerHTML = '<pre>' + logs.slice(-10).join('\n') + '</pre>';
        }
        
        function clearLogs() {
            logs = [];
            updateLogs();
        }
        
        function checkAuth() {
            const statusDiv = document.getElementById('authStatus');
            const storageDiv = document.getElementById('storageData');
            
            // Check auth manager status
            const token = authManager.accessToken;
            const user = authManager.currentUser;
            const isAuth = authManager.isAuthenticated();
            
            // Check localStorage
            const storedToken = localStorage.getItem('accessToken');
            const storedUser = localStorage.getItem('currentUser');
            
            statusDiv.innerHTML = `
                <div class="status ${isAuth ? 'success' : 'error'}">
                    <strong>Authentication Status: ${isAuth ? '✅ AUTHENTICATED' : '❌ NOT AUTHENTICATED'}</strong>
                </div>
                <p><strong>AuthManager Token:</strong> ${token ? '✅ EXISTS' : '❌ MISSING'}</p>
                <p><strong>AuthManager User:</strong> ${user ? '✅ ' + user.username + ' (' + user.role + ')' : '❌ MISSING'}</p>
            `;
            
            storageDiv.innerHTML = `
                <p><strong>LocalStorage Token:</strong> ${storedToken ? '✅ EXISTS' : '❌ MISSING'}</p>
                <p><strong>LocalStorage User:</strong> ${storedUser ? '✅ EXISTS' : '❌ MISSING'}</p>
                ${storedToken ? '<p><strong>Token:</strong> ' + storedToken.substring(0, 30) + '...</p>' : ''}
                ${storedUser ? '<pre>' + JSON.stringify(JSON.parse(storedUser), null, 2) + '</pre>' : ''}
            `;
        }
        
        function clearAuth() {
            authManager.accessToken = null;
            authManager.currentUser = null;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('currentUser');
            checkAuth();
            console.log('Authentication data cleared');
        }
        
        function goToLogin() {
            window.location.href = 'frontend/index.html';
        }
        
        function goToDashboard() {
            window.location.href = 'frontend/dashboard.html';
        }
        
        async function testLogin() {
            try {
                console.log('Testing login...');
                await authManager.login('admin', 'admin');
                checkAuth();
            } catch (error) {
                console.error('Test login failed:', error);
                checkAuth();
            }
        }
        
        // Initial check
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            console.log('Debug page loaded');
        });
    </script>
</body>
</html>
