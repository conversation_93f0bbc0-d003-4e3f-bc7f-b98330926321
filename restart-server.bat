@echo off
echo ========================================
echo   RESTARTING SERVER WITH FIXES
echo ========================================
echo.

echo [1/3] Stopping any running servers...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [2/3] Syncing frontend files...
xcopy frontend public\frontend /E /I /Y >nul

echo [3/3] Starting server...
echo.
echo ========================================
echo   LOGIN SHOULD NOW WORK!
echo ========================================
echo.
echo Server starting on: http://localhost:3000
echo.
echo FIXED ISSUES:
echo - ✅ Login API now works without database
echo - ✅ Dashboard API provides demo data
echo - ✅ All endpoints are simplified
echo.
echo LOGIN CREDENTIALS:
echo   Username: admin
echo   Password: admin
echo.
echo TEST PAGES:
echo   - Main Login: http://localhost:3000/frontend/index.html
echo   - Test Login: http://localhost:3000/test-login.html
echo   - API Test: http://localhost:3000/api/test
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

npm run dev
