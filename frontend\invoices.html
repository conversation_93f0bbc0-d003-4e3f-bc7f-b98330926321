<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Management - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item active">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>Invoice Management</h1>
                <p>Create, manage, and track your invoices</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreateInvoiceModal()">
                    <i class="fas fa-plus"></i>
                    Create Invoice
                </button>
            </div>
        </header>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search invoices..." onkeyup="filterInvoices()">
            </div>
            <div class="filter-controls">
                <select id="statusFilter" onchange="filterInvoices()">
                    <option value="all">All Status</option>
                    <option value="draft">Draft</option>
                    <option value="sent">Sent</option>
                    <option value="paid">Paid</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                </select>
                <select id="periodFilter" onchange="filterInvoices()">
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                    <option value="year">This Year</option>
                </select>
                <button class="btn btn-secondary" onclick="exportInvoices()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Invoices Table -->
        <div class="table-container">
            <table class="data-table" id="invoicesTable">
                <thead>
                    <tr>
                        <th>Invoice #</th>
                        <th>Customer</th>
                        <th>Date</th>
                        <th>Due Date</th>
                        <th>Amount</th>
                        <th>Paid</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="invoicesTableBody">
                    <!-- Invoices will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 0 of 0 invoices</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="changePage(-1)" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span class="page-numbers" id="pageNumbers"></span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="changePage(1)" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Create/Edit Invoice Modal -->
    <div class="modal" id="invoiceModal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="modalTitle">Create New Invoice</h3>
                <button class="modal-close" onclick="closeInvoiceModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="invoiceForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerId">Customer *</label>
                            <select id="customerId" name="customer_id" required>
                                <option value="">Select Customer</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="invoiceDate">Invoice Date *</label>
                            <input type="date" id="invoiceDate" name="invoice_date" required>
                        </div>
                        <div class="form-group">
                            <label for="dueDate">Due Date *</label>
                            <input type="date" id="dueDate" name="due_date" required>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="invoice-items-section">
                        <div class="section-header">
                            <h4>Invoice Items</h4>
                            <button type="button" class="btn btn-secondary" onclick="addInvoiceItem()">
                                <i class="fas fa-plus"></i>
                                Add Item
                            </button>
                        </div>
                        <div class="items-container" id="invoiceItems">
                            <!-- Items will be added here -->
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="discountAmount">Discount Amount</label>
                            <input type="number" id="discountAmount" name="discount_amount" step="0.01" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label for="taxRate">Tax Rate (%)</label>
                            <input type="number" id="taxRate" name="tax_rate" step="0.01" min="0" max="100" value="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="Additional notes or terms..."></textarea>
                    </div>

                    <!-- Invoice Summary -->
                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span id="subtotalAmount">$0.00</span>
                        </div>
                        <div class="summary-row">
                            <span>Discount:</span>
                            <span id="discountAmountDisplay">$0.00</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span id="taxAmountDisplay">$0.00</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span id="totalAmount">$0.00</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeInvoiceModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveInvoice()">
                    <i class="fas fa-save"></i>
                    Save Invoice
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/invoices.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Invoices page loading...');

            // Check authentication with delay to ensure auth manager is ready
            setTimeout(() => {
                if (!requireAuth()) {
                    console.log('❌ Authentication failed, stopping page initialization');
                    return;
                }

                console.log('✅ Authentication successful, initializing invoices page...');
                // Initialize invoices page
                initializeInvoicesPage();
            }, 50);
        });
    </script>
</body>
</html>
