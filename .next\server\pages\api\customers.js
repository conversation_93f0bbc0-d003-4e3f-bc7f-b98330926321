"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/customers";
exports.ids = ["pages/api/customers"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "crypto-js":
/*!****************************!*\
  !*** external "crypto-js" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("crypto-js");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "qrcode":
/*!*************************!*\
  !*** external "qrcode" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("qrcode");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccustomers%5Cindex.js&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccustomers%5Cindex.js&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_customers_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\customers\\index.js */ \"(api)/./pages/api/customers/index.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_customers_index_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_customers_index_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/customers\",\n        pathname: \"/api/customers\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_customers_index_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccustomers%5Cindex.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encryptionUtils: () => (/* binding */ encryptionUtils),\n/* harmony export */   jwtUtils: () => (/* binding */ jwtUtils),\n/* harmony export */   mfaUtils: () => (/* binding */ mfaUtils),\n/* harmony export */   passwordUtils: () => (/* binding */ passwordUtils),\n/* harmony export */   rbacUtils: () => (/* binding */ rbacUtils),\n/* harmony export */   securityUtils: () => (/* binding */ securityUtils),\n/* harmony export */   sessionUtils: () => (/* binding */ sessionUtils)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var speakeasy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! speakeasy */ \"speakeasy\");\n/* harmony import */ var speakeasy__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(speakeasy__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! qrcode */ \"qrcode\");\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(qrcode__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! crypto-js */ \"crypto-js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_4__);\n// Authentication and authorization utilities\n\n\n\n\n\nconst JWT_SECRET = \"your-super-secret-jwt-key-change-in-production-min-32-chars\" || 0;\nconst ENCRYPTION_KEY = \"your-32-character-encryption-key-here\" || 0;\n// Password utilities\nconst passwordUtils = {\n    // Hash password\n    async hashPassword (password) {\n        const saltRounds = 12;\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, saltRounds);\n    },\n    // Verify password\n    async verifyPassword (password, hash) {\n        return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, hash);\n    },\n    // Generate secure password\n    generateSecurePassword (length = 12) {\n        const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\n        let password = \"\";\n        for(let i = 0; i < length; i++){\n            password += charset.charAt(Math.floor(Math.random() * charset.length));\n        }\n        return password;\n    }\n};\n// JWT utilities\nconst jwtUtils = {\n    // Generate JWT token\n    generateToken (payload, expiresIn = \"24h\") {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n            expiresIn\n        });\n    },\n    // Verify JWT token\n    verifyToken (token) {\n        try {\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid or expired token\");\n        }\n    },\n    // Generate refresh token\n    generateRefreshToken (payload) {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n            expiresIn: \"7d\"\n        });\n    }\n};\n// MFA utilities\nconst mfaUtils = {\n    // Generate MFA secret\n    generateSecret (userEmail) {\n        return speakeasy__WEBPACK_IMPORTED_MODULE_2___default().generateSecret({\n            name: `Enterprise Accounting (${userEmail})`,\n            issuer: \"Enterprise Accounting Software\",\n            length: 32\n        });\n    },\n    // Generate QR code for MFA setup\n    async generateQRCode (secret) {\n        try {\n            return await qrcode__WEBPACK_IMPORTED_MODULE_3___default().toDataURL(secret.otpauth_url);\n        } catch (error) {\n            throw new Error(\"Failed to generate QR code\");\n        }\n    },\n    // Verify MFA token\n    verifyToken (token, secret) {\n        return speakeasy__WEBPACK_IMPORTED_MODULE_2___default().totp.verify({\n            secret: secret,\n            encoding: \"base32\",\n            token: token,\n            window: 2 // Allow 2 time steps (60 seconds) tolerance\n        });\n    },\n    // Generate backup codes\n    generateBackupCodes (count = 10) {\n        const codes = [];\n        for(let i = 0; i < count; i++){\n            const code = Math.random().toString(36).substring(2, 10).toUpperCase();\n            codes.push(code);\n        }\n        return codes;\n    }\n};\n// Encryption utilities\nconst encryptionUtils = {\n    // Encrypt sensitive data\n    encrypt (text) {\n        return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().AES.encrypt(text, ENCRYPTION_KEY).toString();\n    },\n    // Decrypt sensitive data\n    decrypt (ciphertext) {\n        const bytes = crypto_js__WEBPACK_IMPORTED_MODULE_4___default().AES.decrypt(ciphertext, ENCRYPTION_KEY);\n        return bytes.toString((crypto_js__WEBPACK_IMPORTED_MODULE_4___default().enc).Utf8);\n    },\n    // Hash sensitive data (one-way)\n    hash (text) {\n        return crypto_js__WEBPACK_IMPORTED_MODULE_4___default().SHA256(text).toString();\n    }\n};\n// Role-based access control\nconst rbacUtils = {\n    // Define role permissions with strict limitations for non-admin users\n    rolePermissions: {\n        admin: [\n            // Full system access - only admin can manage users and system settings\n            \"users.create\",\n            \"users.read\",\n            \"users.update\",\n            \"users.delete\",\n            \"companies.create\",\n            \"companies.read\",\n            \"companies.update\",\n            \"companies.delete\",\n            \"accounts.create\",\n            \"accounts.read\",\n            \"accounts.update\",\n            \"accounts.delete\",\n            \"invoices.create\",\n            \"invoices.read\",\n            \"invoices.update\",\n            \"invoices.delete\",\n            \"expenses.create\",\n            \"expenses.read\",\n            \"expenses.update\",\n            \"expenses.delete\",\n            \"expenses.approve\",\n            \"payroll.create\",\n            \"payroll.read\",\n            \"payroll.update\",\n            \"payroll.delete\",\n            \"reports.read\",\n            \"reports.export\",\n            \"reports.create\",\n            \"settings.read\",\n            \"settings.update\",\n            \"audit.read\",\n            \"audit.export\",\n            \"customers.create\",\n            \"customers.read\",\n            \"customers.update\",\n            \"customers.delete\",\n            \"suppliers.create\",\n            \"suppliers.read\",\n            \"suppliers.update\",\n            \"suppliers.delete\",\n            \"employees.create\",\n            \"employees.read\",\n            \"employees.update\",\n            \"employees.delete\",\n            \"dashboard.read\",\n            \"dashboard.admin\"\n        ],\n        manager: [\n            // Limited management access - cannot manage users or system settings\n            \"invoices.read\",\n            \"invoices.update\",\n            \"expenses.read\",\n            \"expenses.approve\",\n            \"payroll.read\",\n            \"reports.read\",\n            \"customers.read\",\n            \"suppliers.read\",\n            \"employees.read\",\n            \"dashboard.read\"\n        ],\n        accountant: [\n            // Accounting operations only - cannot approve expenses or manage users\n            \"invoices.create\",\n            \"invoices.read\",\n            \"invoices.update\",\n            \"expenses.create\",\n            \"expenses.read\",\n            \"payroll.read\",\n            \"reports.read\",\n            \"customers.read\",\n            \"customers.update\",\n            \"suppliers.read\",\n            \"suppliers.update\",\n            \"dashboard.read\"\n        ],\n        auditor: [\n            // Read-only access for auditing purposes\n            \"invoices.read\",\n            \"expenses.read\",\n            \"payroll.read\",\n            \"reports.read\",\n            \"audit.read\",\n            \"customers.read\",\n            \"suppliers.read\",\n            \"employees.read\",\n            \"dashboard.read\"\n        ],\n        employee: [\n            // Very limited access - only own expenses and payroll\n            \"expenses.create\",\n            \"expenses.read.own\",\n            \"payroll.read.own\"\n        ]\n    },\n    // Check if user has permission with strict admin-only restrictions\n    hasPermission (userRole, permission) {\n        // Only admin can access certain critical functions\n        const adminOnlyPermissions = [\n            \"users.create\",\n            \"users.update\",\n            \"users.delete\",\n            \"companies.create\",\n            \"companies.update\",\n            \"companies.delete\",\n            \"settings.read\",\n            \"settings.update\",\n            \"audit.read\",\n            \"audit.export\",\n            \"dashboard.admin\"\n        ];\n        if (adminOnlyPermissions.includes(permission) && userRole !== \"admin\") {\n            return false;\n        }\n        const permissions = this.rolePermissions[userRole] || [];\n        return permissions.includes(permission);\n    },\n    // Check multiple permissions\n    hasAnyPermission (userRole, permissions) {\n        return permissions.some((permission)=>this.hasPermission(userRole, permission));\n    },\n    // Check all permissions\n    hasAllPermissions (userRole, permissions) {\n        return permissions.every((permission)=>this.hasPermission(userRole, permission));\n    },\n    // Check if user can access specific resource (for own data restrictions)\n    canAccessResource (userRole, userId, resourceUserId, permission) {\n        // Admin can access everything\n        if (userRole === \"admin\") {\n            return true;\n        }\n        // For \"own\" permissions, check if user is accessing their own data\n        if (permission.endsWith(\".own\")) {\n            return userId === resourceUserId;\n        }\n        // Regular permission check\n        return this.hasPermission(userRole, permission);\n    },\n    // Get user limitations based on role\n    getUserLimitations (userRole) {\n        const limitations = {\n            admin: {\n                canManageUsers: true,\n                canManageSettings: true,\n                canViewAuditTrail: true,\n                canApproveExpenses: true,\n                canDeleteRecords: true,\n                canExportData: true,\n                canAccessAllData: true,\n                maxRecordsPerPage: 1000\n            },\n            manager: {\n                canManageUsers: false,\n                canManageSettings: false,\n                canViewAuditTrail: false,\n                canApproveExpenses: true,\n                canDeleteRecords: false,\n                canExportData: false,\n                canAccessAllData: true,\n                maxRecordsPerPage: 500\n            },\n            accountant: {\n                canManageUsers: false,\n                canManageSettings: false,\n                canViewAuditTrail: false,\n                canApproveExpenses: false,\n                canDeleteRecords: false,\n                canExportData: false,\n                canAccessAllData: true,\n                maxRecordsPerPage: 200\n            },\n            auditor: {\n                canManageUsers: false,\n                canManageSettings: false,\n                canViewAuditTrail: true,\n                canApproveExpenses: false,\n                canDeleteRecords: false,\n                canExportData: true,\n                canAccessAllData: true,\n                maxRecordsPerPage: 100,\n                readOnlyAccess: true\n            },\n            employee: {\n                canManageUsers: false,\n                canManageSettings: false,\n                canViewAuditTrail: false,\n                canApproveExpenses: false,\n                canDeleteRecords: false,\n                canExportData: false,\n                canAccessAllData: false,\n                maxRecordsPerPage: 50,\n                ownDataOnly: true\n            }\n        };\n        return limitations[userRole] || limitations.employee;\n    }\n};\n// Session management\nconst sessionUtils = {\n    // Create session data\n    createSession (user) {\n        return {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            role: user.role,\n            firstName: user.first_name,\n            lastName: user.last_name,\n            isActive: user.is_active,\n            mfaEnabled: user.mfa_enabled,\n            loginTime: new Date().toISOString()\n        };\n    },\n    // Validate session\n    validateSession (session) {\n        if (!session || !session.id || !session.role) {\n            return false;\n        }\n        // Check if session is expired (24 hours)\n        const loginTime = new Date(session.loginTime);\n        const now = new Date();\n        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);\n        return hoursDiff < 24;\n    }\n};\n// Security utilities\nconst securityUtils = {\n    // Generate secure random string\n    generateSecureRandom (length = 32) {\n        const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz\";\n        let result = \"\";\n        for(let i = 0; i < length; i++){\n            result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n    },\n    // Validate IP address\n    isValidIP (ip) {\n        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;\n        return ipv4Regex.test(ip) || ipv6Regex.test(ip);\n    },\n    // Sanitize input\n    sanitizeInput (input) {\n        if (typeof input !== \"string\") return input;\n        return input.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, \"\").replace(/javascript:/gi, \"\").replace(/on\\w+\\s*=/gi, \"\");\n    },\n    // Rate limiting helper\n    createRateLimiter (windowMs = 15 * 60 * 1000, max = 100) {\n        const requests = new Map();\n        return (identifier)=>{\n            const now = Date.now();\n            const windowStart = now - windowMs;\n            // Clean old entries\n            for (const [key, timestamps] of requests.entries()){\n                const validTimestamps = timestamps.filter((time)=>time > windowStart);\n                if (validTimestamps.length === 0) {\n                    requests.delete(key);\n                } else {\n                    requests.set(key, validTimestamps);\n                }\n            }\n            // Check current identifier\n            const userRequests = requests.get(identifier) || [];\n            const recentRequests = userRequests.filter((time)=>time > windowStart);\n            if (recentRequests.length >= max) {\n                return false; // Rate limit exceeded\n            }\n            recentRequests.push(now);\n            requests.set(identifier, recentRequests);\n            return true; // Request allowed\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    passwordUtils,\n    jwtUtils,\n    mfaUtils,\n    encryptionUtils,\n    rbacUtils,\n    sessionUtils,\n    securityUtils\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvYXV0aC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw2Q0FBNkM7QUFDZDtBQUNEO0FBQ0k7QUFDTjtBQUNLO0FBRWpDLE1BQU1LLGFBQWFDLDZEQUFzQixJQUFJO0FBQzdDLE1BQU1FLGlCQUFpQkYsdUNBQTBCLElBQUk7QUFFckQscUJBQXFCO0FBQ2QsTUFBTUcsZ0JBQWdCO0lBQzNCLGdCQUFnQjtJQUNoQixNQUFNQyxjQUFhQyxRQUFRO1FBQ3pCLE1BQU1DLGFBQWE7UUFDbkIsT0FBTyxNQUFNWCxvREFBVyxDQUFDVSxVQUFVQztJQUNyQztJQUVBLGtCQUFrQjtJQUNsQixNQUFNRSxnQkFBZUgsUUFBUSxFQUFFRSxJQUFJO1FBQ2pDLE9BQU8sTUFBTVosdURBQWMsQ0FBQ1UsVUFBVUU7SUFDeEM7SUFFQSwyQkFBMkI7SUFDM0JHLHdCQUF1QkMsU0FBUyxFQUFFO1FBQ2hDLE1BQU1DLFVBQVU7UUFDaEIsSUFBSVAsV0FBVztRQUNmLElBQUssSUFBSVEsSUFBSSxHQUFHQSxJQUFJRixRQUFRRSxJQUFLO1lBQy9CUixZQUFZTyxRQUFRRSxNQUFNLENBQUNDLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLTCxRQUFRRCxNQUFNO1FBQ3RFO1FBQ0EsT0FBT047SUFDVDtBQUNGLEVBQUU7QUFFRixnQkFBZ0I7QUFDVCxNQUFNYSxXQUFXO0lBQ3RCLHFCQUFxQjtJQUNyQkMsZUFBY0MsT0FBTyxFQUFFQyxZQUFZLEtBQUs7UUFDdEMsT0FBTzNCLHdEQUFRLENBQUMwQixTQUFTckIsWUFBWTtZQUFFc0I7UUFBVTtJQUNuRDtJQUVBLG1CQUFtQjtJQUNuQkUsYUFBWUMsS0FBSztRQUNmLElBQUk7WUFDRixPQUFPOUIsMERBQVUsQ0FBQzhCLE9BQU96QjtRQUMzQixFQUFFLE9BQU8yQixPQUFPO1lBQ2QsTUFBTSxJQUFJQyxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQSx5QkFBeUI7SUFDekJDLHNCQUFxQlIsT0FBTztRQUMxQixPQUFPMUIsd0RBQVEsQ0FBQzBCLFNBQVNyQixZQUFZO1lBQUVzQixXQUFXO1FBQUs7SUFDekQ7QUFDRixFQUFFO0FBRUYsZ0JBQWdCO0FBQ1QsTUFBTVEsV0FBVztJQUN0QixzQkFBc0I7SUFDdEJDLGdCQUFlQyxTQUFTO1FBQ3RCLE9BQU9uQywrREFBd0IsQ0FBQztZQUM5Qm9DLE1BQU0sQ0FBQyx1QkFBdUIsRUFBRUQsVUFBVSxDQUFDLENBQUM7WUFDNUNFLFFBQVE7WUFDUnRCLFFBQVE7UUFDVjtJQUNGO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU11QixnQkFBZUMsTUFBTTtRQUN6QixJQUFJO1lBQ0YsT0FBTyxNQUFNdEMsdURBQWdCLENBQUNzQyxPQUFPRSxXQUFXO1FBQ2xELEVBQUUsT0FBT1gsT0FBTztZQUNkLE1BQU0sSUFBSUMsTUFBTTtRQUNsQjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CSixhQUFZQyxLQUFLLEVBQUVXLE1BQU07UUFDdkIsT0FBT3ZDLHFEQUFjLENBQUM2QixNQUFNLENBQUM7WUFDM0JVLFFBQVFBO1lBQ1JJLFVBQVU7WUFDVmYsT0FBT0E7WUFDUGdCLFFBQVEsRUFBRSw0Q0FBNEM7UUFDeEQ7SUFDRjtJQUVBLHdCQUF3QjtJQUN4QkMscUJBQW9CQyxRQUFRLEVBQUU7UUFDNUIsTUFBTUMsUUFBUSxFQUFFO1FBQ2hCLElBQUssSUFBSTlCLElBQUksR0FBR0EsSUFBSTZCLE9BQU83QixJQUFLO1lBQzlCLE1BQU0rQixPQUFPN0IsS0FBS0UsTUFBTSxHQUFHNEIsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxHQUFHLElBQUlDLFdBQVc7WUFDcEVKLE1BQU1LLElBQUksQ0FBQ0o7UUFDYjtRQUNBLE9BQU9EO0lBQ1Q7QUFDRixFQUFFO0FBRUYsdUJBQXVCO0FBQ2hCLE1BQU1NLGtCQUFrQjtJQUM3Qix5QkFBeUI7SUFDekJDLFNBQVFDLElBQUk7UUFDVixPQUFPckQsb0RBQVksQ0FBQ29ELE9BQU8sQ0FBQ0MsTUFBTWpELGdCQUFnQjJDLFFBQVE7SUFDNUQ7SUFFQSx5QkFBeUI7SUFDekJRLFNBQVFDLFVBQVU7UUFDaEIsTUFBTUMsUUFBUXpELG9EQUFZLENBQUN1RCxPQUFPLENBQUNDLFlBQVlwRDtRQUMvQyxPQUFPcUQsTUFBTVYsUUFBUSxDQUFDL0Msc0RBQVksQ0FBQzJELElBQUk7SUFDekM7SUFFQSxnQ0FBZ0M7SUFDaENsRCxNQUFLNEMsSUFBSTtRQUNQLE9BQU9yRCx1REFBZSxDQUFDcUQsTUFBTU4sUUFBUTtJQUN2QztBQUNGLEVBQUU7QUFFRiw0QkFBNEI7QUFDckIsTUFBTWMsWUFBWTtJQUN2QixzRUFBc0U7SUFDdEVDLGlCQUFpQjtRQUNmQyxPQUFPO1lBQ0wsdUVBQXVFO1lBQ3ZFO1lBQWdCO1lBQWM7WUFBZ0I7WUFDOUM7WUFBb0I7WUFBa0I7WUFBb0I7WUFDMUQ7WUFBbUI7WUFBaUI7WUFBbUI7WUFDdkQ7WUFBbUI7WUFBaUI7WUFBbUI7WUFDdkQ7WUFBbUI7WUFBaUI7WUFBbUI7WUFBbUI7WUFDMUU7WUFBa0I7WUFBZ0I7WUFBa0I7WUFDcEQ7WUFBZ0I7WUFBa0I7WUFDbEM7WUFBaUI7WUFDakI7WUFBYztZQUNkO1lBQW9CO1lBQWtCO1lBQW9CO1lBQzFEO1lBQW9CO1lBQWtCO1lBQW9CO1lBQzFEO1lBQW9CO1lBQWtCO1lBQW9CO1lBQzFEO1lBQWtCO1NBQ25CO1FBQ0RDLFNBQVM7WUFDUCxxRUFBcUU7WUFDckU7WUFBaUI7WUFDakI7WUFBaUI7WUFDakI7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsWUFBWTtZQUNWLHVFQUF1RTtZQUN2RTtZQUFtQjtZQUFpQjtZQUNwQztZQUFtQjtZQUNuQjtZQUNBO1lBQ0E7WUFBa0I7WUFDbEI7WUFBa0I7WUFDbEI7U0FDRDtRQUNEQyxTQUFTO1lBQ1AseUNBQXlDO1lBQ3pDO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFVBQVU7WUFDUixzREFBc0Q7WUFDdEQ7WUFDQTtZQUNBO1NBQ0Q7SUFDSDtJQUVBLG1FQUFtRTtJQUNuRUMsZUFBY0MsUUFBUSxFQUFFQyxVQUFVO1FBQ2hDLG1EQUFtRDtRQUNuRCxNQUFNQyx1QkFBdUI7WUFDM0I7WUFBZ0I7WUFBZ0I7WUFDaEM7WUFBb0I7WUFBb0I7WUFDeEM7WUFBaUI7WUFDakI7WUFBYztZQUNkO1NBQ0Q7UUFFRCxJQUFJQSxxQkFBcUJDLFFBQVEsQ0FBQ0YsZUFBZUQsYUFBYSxTQUFTO1lBQ3JFLE9BQU87UUFDVDtRQUVBLE1BQU1JLGNBQWMsSUFBSSxDQUFDWCxlQUFlLENBQUNPLFNBQVMsSUFBSSxFQUFFO1FBQ3hELE9BQU9JLFlBQVlELFFBQVEsQ0FBQ0Y7SUFDOUI7SUFFQSw2QkFBNkI7SUFDN0JJLGtCQUFpQkwsUUFBUSxFQUFFSSxXQUFXO1FBQ3BDLE9BQU9BLFlBQVlFLElBQUksQ0FBQ0wsQ0FBQUEsYUFBYyxJQUFJLENBQUNGLGFBQWEsQ0FBQ0MsVUFBVUM7SUFDckU7SUFFQSx3QkFBd0I7SUFDeEJNLG1CQUFrQlAsUUFBUSxFQUFFSSxXQUFXO1FBQ3JDLE9BQU9BLFlBQVlJLEtBQUssQ0FBQ1AsQ0FBQUEsYUFBYyxJQUFJLENBQUNGLGFBQWEsQ0FBQ0MsVUFBVUM7SUFDdEU7SUFFQSx5RUFBeUU7SUFDekVRLG1CQUFrQlQsUUFBUSxFQUFFVSxNQUFNLEVBQUVDLGNBQWMsRUFBRVYsVUFBVTtRQUM1RCw4QkFBOEI7UUFDOUIsSUFBSUQsYUFBYSxTQUFTO1lBQ3hCLE9BQU87UUFDVDtRQUVBLG1FQUFtRTtRQUNuRSxJQUFJQyxXQUFXVyxRQUFRLENBQUMsU0FBUztZQUMvQixPQUFPRixXQUFXQztRQUNwQjtRQUVBLDJCQUEyQjtRQUMzQixPQUFPLElBQUksQ0FBQ1osYUFBYSxDQUFDQyxVQUFVQztJQUN0QztJQUVBLHFDQUFxQztJQUNyQ1ksb0JBQW1CYixRQUFRO1FBQ3pCLE1BQU1jLGNBQWM7WUFDbEJwQixPQUFPO2dCQUNMcUIsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMsbUJBQW1CO2dCQUNuQkMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsbUJBQW1CO1lBQ3JCO1lBQ0EzQixTQUFTO2dCQUNQb0IsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMsbUJBQW1CO2dCQUNuQkMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsbUJBQW1CO1lBQ3JCO1lBQ0ExQixZQUFZO2dCQUNWbUIsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMsbUJBQW1CO2dCQUNuQkMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsbUJBQW1CO1lBQ3JCO1lBQ0F6QixTQUFTO2dCQUNQa0IsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMsbUJBQW1CO2dCQUNuQkMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsbUJBQW1CO2dCQUNuQkMsZ0JBQWdCO1lBQ2xCO1lBQ0F6QixVQUFVO2dCQUNSaUIsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMsbUJBQW1CO2dCQUNuQkMsb0JBQW9CO2dCQUNwQkMsa0JBQWtCO2dCQUNsQkMsZUFBZTtnQkFDZkMsa0JBQWtCO2dCQUNsQkMsbUJBQW1CO2dCQUNuQkUsYUFBYTtZQUNmO1FBQ0Y7UUFFQSxPQUFPVixXQUFXLENBQUNkLFNBQVMsSUFBSWMsWUFBWWhCLFFBQVE7SUFDdEQ7QUFDRixFQUFFO0FBRUYscUJBQXFCO0FBQ2QsTUFBTTJCLGVBQWU7SUFDMUIsc0JBQXNCO0lBQ3RCQyxlQUFjQyxJQUFJO1FBQ2hCLE9BQU87WUFDTEMsSUFBSUQsS0FBS0MsRUFBRTtZQUNYQyxVQUFVRixLQUFLRSxRQUFRO1lBQ3ZCQyxPQUFPSCxLQUFLRyxLQUFLO1lBQ2pCQyxNQUFNSixLQUFLSSxJQUFJO1lBQ2ZDLFdBQVdMLEtBQUtNLFVBQVU7WUFDMUJDLFVBQVVQLEtBQUtRLFNBQVM7WUFDeEJDLFVBQVVULEtBQUtVLFNBQVM7WUFDeEJDLFlBQVlYLEtBQUtZLFdBQVc7WUFDNUJDLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztRQUNuQztJQUNGO0lBRUEsbUJBQW1CO0lBQ25CQyxpQkFBZ0JDLE9BQU87UUFDckIsSUFBSSxDQUFDQSxXQUFXLENBQUNBLFFBQVFoQixFQUFFLElBQUksQ0FBQ2dCLFFBQVFiLElBQUksRUFBRTtZQUM1QyxPQUFPO1FBQ1Q7UUFFQSx5Q0FBeUM7UUFDekMsTUFBTVMsWUFBWSxJQUFJQyxLQUFLRyxRQUFRSixTQUFTO1FBQzVDLE1BQU1LLE1BQU0sSUFBSUo7UUFDaEIsTUFBTUssWUFBWSxDQUFDRCxNQUFNTCxTQUFRLElBQU0sUUFBTyxLQUFLLEVBQUM7UUFFcEQsT0FBT00sWUFBWTtJQUNyQjtBQUNGLEVBQUU7QUFFRixxQkFBcUI7QUFDZCxNQUFNQyxnQkFBZ0I7SUFDM0IsZ0NBQWdDO0lBQ2hDQyxzQkFBcUJ4RyxTQUFTLEVBQUU7UUFDOUIsTUFBTXlHLFFBQVE7UUFDZCxJQUFJQyxTQUFTO1FBQ2IsSUFBSyxJQUFJeEcsSUFBSSxHQUFHQSxJQUFJRixRQUFRRSxJQUFLO1lBQy9Cd0csVUFBVUQsTUFBTXRHLE1BQU0sQ0FBQ0MsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUttRyxNQUFNekcsTUFBTTtRQUNoRTtRQUNBLE9BQU8wRztJQUNUO0lBRUEsc0JBQXNCO0lBQ3RCQyxXQUFVQyxFQUFFO1FBQ1YsTUFBTUMsWUFBWTtRQUNsQixNQUFNQyxZQUFZO1FBQ2xCLE9BQU9ELFVBQVVFLElBQUksQ0FBQ0gsT0FBT0UsVUFBVUMsSUFBSSxDQUFDSDtJQUM5QztJQUVBLGlCQUFpQjtJQUNqQkksZUFBY0MsS0FBSztRQUNqQixJQUFJLE9BQU9BLFVBQVUsVUFBVSxPQUFPQTtRQUN0QyxPQUFPQSxNQUFNQyxPQUFPLENBQUMsdURBQXVELElBQy9EQSxPQUFPLENBQUMsaUJBQWlCLElBQ3pCQSxPQUFPLENBQUMsZUFBZTtJQUN0QztJQUVBLHVCQUF1QjtJQUN2QkMsbUJBQWtCQyxXQUFXLEtBQUssS0FBSyxJQUFJLEVBQUVDLE1BQU0sR0FBRztRQUNwRCxNQUFNQyxXQUFXLElBQUlDO1FBRXJCLE9BQU8sQ0FBQ0M7WUFDTixNQUFNbkIsTUFBTUosS0FBS0ksR0FBRztZQUNwQixNQUFNb0IsY0FBY3BCLE1BQU1lO1lBRTFCLG9CQUFvQjtZQUNwQixLQUFLLE1BQU0sQ0FBQ00sS0FBS0MsV0FBVyxJQUFJTCxTQUFTTSxPQUFPLEdBQUk7Z0JBQ2xELE1BQU1DLGtCQUFrQkYsV0FBV0csTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxPQUFPTjtnQkFDekQsSUFBSUksZ0JBQWdCN0gsTUFBTSxLQUFLLEdBQUc7b0JBQ2hDc0gsU0FBU1UsTUFBTSxDQUFDTjtnQkFDbEIsT0FBTztvQkFDTEosU0FBU1csR0FBRyxDQUFDUCxLQUFLRztnQkFDcEI7WUFDRjtZQUVBLDJCQUEyQjtZQUMzQixNQUFNSyxlQUFlWixTQUFTYSxHQUFHLENBQUNYLGVBQWUsRUFBRTtZQUNuRCxNQUFNWSxpQkFBaUJGLGFBQWFKLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsT0FBT047WUFFMUQsSUFBSVcsZUFBZXBJLE1BQU0sSUFBSXFILEtBQUs7Z0JBQ2hDLE9BQU8sT0FBTyxzQkFBc0I7WUFDdEM7WUFFQWUsZUFBZS9GLElBQUksQ0FBQ2dFO1lBQ3BCaUIsU0FBU1csR0FBRyxDQUFDVCxZQUFZWTtZQUN6QixPQUFPLE1BQU0sa0JBQWtCO1FBQ2pDO0lBQ0Y7QUFDRixFQUFFO0FBRUYsaUVBQWU7SUFDYjVJO0lBQ0FlO0lBQ0FXO0lBQ0FvQjtJQUNBVTtJQUNBaUM7SUFDQXNCO0FBQ0YsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW50ZXJwcmlzZS1hY2NvdW50aW5nLXNvZnR3YXJlLy4vbGliL2F1dGguanM/Mjg3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBdXRoZW50aWNhdGlvbiBhbmQgYXV0aG9yaXphdGlvbiB1dGlsaXRpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xuaW1wb3J0IHNwZWFrZWFzeSBmcm9tICdzcGVha2Vhc3knO1xuaW1wb3J0IFFSQ29kZSBmcm9tICdxcmNvZGUnO1xuaW1wb3J0IENyeXB0b0pTIGZyb20gJ2NyeXB0by1qcyc7XG5cbmNvbnN0IEpXVF9TRUNSRVQgPSBwcm9jZXNzLmVudi5KV1RfU0VDUkVUIHx8ICd5b3VyLXN1cGVyLXNlY3JldC1qd3Qta2V5LWNoYW5nZS1pbi1wcm9kdWN0aW9uJztcbmNvbnN0IEVOQ1JZUFRJT05fS0VZID0gcHJvY2Vzcy5lbnYuRU5DUllQVElPTl9LRVkgfHwgJ3lvdXItMzItY2hhcmFjdGVyLWVuY3J5cHRpb24ta2V5JztcblxuLy8gUGFzc3dvcmQgdXRpbGl0aWVzXG5leHBvcnQgY29uc3QgcGFzc3dvcmRVdGlscyA9IHtcbiAgLy8gSGFzaCBwYXNzd29yZFxuICBhc3luYyBoYXNoUGFzc3dvcmQocGFzc3dvcmQpIHtcbiAgICBjb25zdCBzYWx0Um91bmRzID0gMTI7XG4gICAgcmV0dXJuIGF3YWl0IGJjcnlwdC5oYXNoKHBhc3N3b3JkLCBzYWx0Um91bmRzKTtcbiAgfSxcblxuICAvLyBWZXJpZnkgcGFzc3dvcmRcbiAgYXN5bmMgdmVyaWZ5UGFzc3dvcmQocGFzc3dvcmQsIGhhc2gpIHtcbiAgICByZXR1cm4gYXdhaXQgYmNyeXB0LmNvbXBhcmUocGFzc3dvcmQsIGhhc2gpO1xuICB9LFxuXG4gIC8vIEdlbmVyYXRlIHNlY3VyZSBwYXNzd29yZFxuICBnZW5lcmF0ZVNlY3VyZVBhc3N3b3JkKGxlbmd0aCA9IDEyKSB7XG4gICAgY29uc3QgY2hhcnNldCA9ICdhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ekFCQ0RFRkdISUpLTE1OT1BRUlNUVVZXWFlaMDEyMzQ1Njc4OSFAIyQlXiYqJztcbiAgICBsZXQgcGFzc3dvcmQgPSAnJztcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgICBwYXNzd29yZCArPSBjaGFyc2V0LmNoYXJBdChNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjaGFyc2V0Lmxlbmd0aCkpO1xuICAgIH1cbiAgICByZXR1cm4gcGFzc3dvcmQ7XG4gIH1cbn07XG5cbi8vIEpXVCB1dGlsaXRpZXNcbmV4cG9ydCBjb25zdCBqd3RVdGlscyA9IHtcbiAgLy8gR2VuZXJhdGUgSldUIHRva2VuXG4gIGdlbmVyYXRlVG9rZW4ocGF5bG9hZCwgZXhwaXJlc0luID0gJzI0aCcpIHtcbiAgICByZXR1cm4gand0LnNpZ24ocGF5bG9hZCwgSldUX1NFQ1JFVCwgeyBleHBpcmVzSW4gfSk7XG4gIH0sXG5cbiAgLy8gVmVyaWZ5IEpXVCB0b2tlblxuICB2ZXJpZnlUb2tlbih0b2tlbikge1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gand0LnZlcmlmeSh0b2tlbiwgSldUX1NFQ1JFVCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBvciBleHBpcmVkIHRva2VuJyk7XG4gICAgfVxuICB9LFxuXG4gIC8vIEdlbmVyYXRlIHJlZnJlc2ggdG9rZW5cbiAgZ2VuZXJhdGVSZWZyZXNoVG9rZW4ocGF5bG9hZCkge1xuICAgIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbiAgfVxufTtcblxuLy8gTUZBIHV0aWxpdGllc1xuZXhwb3J0IGNvbnN0IG1mYVV0aWxzID0ge1xuICAvLyBHZW5lcmF0ZSBNRkEgc2VjcmV0XG4gIGdlbmVyYXRlU2VjcmV0KHVzZXJFbWFpbCkge1xuICAgIHJldHVybiBzcGVha2Vhc3kuZ2VuZXJhdGVTZWNyZXQoe1xuICAgICAgbmFtZTogYEVudGVycHJpc2UgQWNjb3VudGluZyAoJHt1c2VyRW1haWx9KWAsXG4gICAgICBpc3N1ZXI6ICdFbnRlcnByaXNlIEFjY291bnRpbmcgU29mdHdhcmUnLFxuICAgICAgbGVuZ3RoOiAzMlxuICAgIH0pO1xuICB9LFxuXG4gIC8vIEdlbmVyYXRlIFFSIGNvZGUgZm9yIE1GQSBzZXR1cFxuICBhc3luYyBnZW5lcmF0ZVFSQ29kZShzZWNyZXQpIHtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGF3YWl0IFFSQ29kZS50b0RhdGFVUkwoc2VjcmV0Lm90cGF1dGhfdXJsKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgUVIgY29kZScpO1xuICAgIH1cbiAgfSxcblxuICAvLyBWZXJpZnkgTUZBIHRva2VuXG4gIHZlcmlmeVRva2VuKHRva2VuLCBzZWNyZXQpIHtcbiAgICByZXR1cm4gc3BlYWtlYXN5LnRvdHAudmVyaWZ5KHtcbiAgICAgIHNlY3JldDogc2VjcmV0LFxuICAgICAgZW5jb2Rpbmc6ICdiYXNlMzInLFxuICAgICAgdG9rZW46IHRva2VuLFxuICAgICAgd2luZG93OiAyIC8vIEFsbG93IDIgdGltZSBzdGVwcyAoNjAgc2Vjb25kcykgdG9sZXJhbmNlXG4gICAgfSk7XG4gIH0sXG5cbiAgLy8gR2VuZXJhdGUgYmFja3VwIGNvZGVzXG4gIGdlbmVyYXRlQmFja3VwQ29kZXMoY291bnQgPSAxMCkge1xuICAgIGNvbnN0IGNvZGVzID0gW107XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjb3VudDsgaSsrKSB7XG4gICAgICBjb25zdCBjb2RlID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDEwKS50b1VwcGVyQ2FzZSgpO1xuICAgICAgY29kZXMucHVzaChjb2RlKTtcbiAgICB9XG4gICAgcmV0dXJuIGNvZGVzO1xuICB9XG59O1xuXG4vLyBFbmNyeXB0aW9uIHV0aWxpdGllc1xuZXhwb3J0IGNvbnN0IGVuY3J5cHRpb25VdGlscyA9IHtcbiAgLy8gRW5jcnlwdCBzZW5zaXRpdmUgZGF0YVxuICBlbmNyeXB0KHRleHQpIHtcbiAgICByZXR1cm4gQ3J5cHRvSlMuQUVTLmVuY3J5cHQodGV4dCwgRU5DUllQVElPTl9LRVkpLnRvU3RyaW5nKCk7XG4gIH0sXG5cbiAgLy8gRGVjcnlwdCBzZW5zaXRpdmUgZGF0YVxuICBkZWNyeXB0KGNpcGhlcnRleHQpIHtcbiAgICBjb25zdCBieXRlcyA9IENyeXB0b0pTLkFFUy5kZWNyeXB0KGNpcGhlcnRleHQsIEVOQ1JZUFRJT05fS0VZKTtcbiAgICByZXR1cm4gYnl0ZXMudG9TdHJpbmcoQ3J5cHRvSlMuZW5jLlV0ZjgpO1xuICB9LFxuXG4gIC8vIEhhc2ggc2Vuc2l0aXZlIGRhdGEgKG9uZS13YXkpXG4gIGhhc2godGV4dCkge1xuICAgIHJldHVybiBDcnlwdG9KUy5TSEEyNTYodGV4dCkudG9TdHJpbmcoKTtcbiAgfVxufTtcblxuLy8gUm9sZS1iYXNlZCBhY2Nlc3MgY29udHJvbFxuZXhwb3J0IGNvbnN0IHJiYWNVdGlscyA9IHtcbiAgLy8gRGVmaW5lIHJvbGUgcGVybWlzc2lvbnMgd2l0aCBzdHJpY3QgbGltaXRhdGlvbnMgZm9yIG5vbi1hZG1pbiB1c2Vyc1xuICByb2xlUGVybWlzc2lvbnM6IHtcbiAgICBhZG1pbjogW1xuICAgICAgLy8gRnVsbCBzeXN0ZW0gYWNjZXNzIC0gb25seSBhZG1pbiBjYW4gbWFuYWdlIHVzZXJzIGFuZCBzeXN0ZW0gc2V0dGluZ3NcbiAgICAgICd1c2Vycy5jcmVhdGUnLCAndXNlcnMucmVhZCcsICd1c2Vycy51cGRhdGUnLCAndXNlcnMuZGVsZXRlJyxcbiAgICAgICdjb21wYW5pZXMuY3JlYXRlJywgJ2NvbXBhbmllcy5yZWFkJywgJ2NvbXBhbmllcy51cGRhdGUnLCAnY29tcGFuaWVzLmRlbGV0ZScsXG4gICAgICAnYWNjb3VudHMuY3JlYXRlJywgJ2FjY291bnRzLnJlYWQnLCAnYWNjb3VudHMudXBkYXRlJywgJ2FjY291bnRzLmRlbGV0ZScsXG4gICAgICAnaW52b2ljZXMuY3JlYXRlJywgJ2ludm9pY2VzLnJlYWQnLCAnaW52b2ljZXMudXBkYXRlJywgJ2ludm9pY2VzLmRlbGV0ZScsXG4gICAgICAnZXhwZW5zZXMuY3JlYXRlJywgJ2V4cGVuc2VzLnJlYWQnLCAnZXhwZW5zZXMudXBkYXRlJywgJ2V4cGVuc2VzLmRlbGV0ZScsICdleHBlbnNlcy5hcHByb3ZlJyxcbiAgICAgICdwYXlyb2xsLmNyZWF0ZScsICdwYXlyb2xsLnJlYWQnLCAncGF5cm9sbC51cGRhdGUnLCAncGF5cm9sbC5kZWxldGUnLFxuICAgICAgJ3JlcG9ydHMucmVhZCcsICdyZXBvcnRzLmV4cG9ydCcsICdyZXBvcnRzLmNyZWF0ZScsXG4gICAgICAnc2V0dGluZ3MucmVhZCcsICdzZXR0aW5ncy51cGRhdGUnLFxuICAgICAgJ2F1ZGl0LnJlYWQnLCAnYXVkaXQuZXhwb3J0JyxcbiAgICAgICdjdXN0b21lcnMuY3JlYXRlJywgJ2N1c3RvbWVycy5yZWFkJywgJ2N1c3RvbWVycy51cGRhdGUnLCAnY3VzdG9tZXJzLmRlbGV0ZScsXG4gICAgICAnc3VwcGxpZXJzLmNyZWF0ZScsICdzdXBwbGllcnMucmVhZCcsICdzdXBwbGllcnMudXBkYXRlJywgJ3N1cHBsaWVycy5kZWxldGUnLFxuICAgICAgJ2VtcGxveWVlcy5jcmVhdGUnLCAnZW1wbG95ZWVzLnJlYWQnLCAnZW1wbG95ZWVzLnVwZGF0ZScsICdlbXBsb3llZXMuZGVsZXRlJyxcbiAgICAgICdkYXNoYm9hcmQucmVhZCcsICdkYXNoYm9hcmQuYWRtaW4nXG4gICAgXSxcbiAgICBtYW5hZ2VyOiBbXG4gICAgICAvLyBMaW1pdGVkIG1hbmFnZW1lbnQgYWNjZXNzIC0gY2Fubm90IG1hbmFnZSB1c2VycyBvciBzeXN0ZW0gc2V0dGluZ3NcbiAgICAgICdpbnZvaWNlcy5yZWFkJywgJ2ludm9pY2VzLnVwZGF0ZScsXG4gICAgICAnZXhwZW5zZXMucmVhZCcsICdleHBlbnNlcy5hcHByb3ZlJyxcbiAgICAgICdwYXlyb2xsLnJlYWQnLFxuICAgICAgJ3JlcG9ydHMucmVhZCcsXG4gICAgICAnY3VzdG9tZXJzLnJlYWQnLFxuICAgICAgJ3N1cHBsaWVycy5yZWFkJyxcbiAgICAgICdlbXBsb3llZXMucmVhZCcsXG4gICAgICAnZGFzaGJvYXJkLnJlYWQnXG4gICAgXSxcbiAgICBhY2NvdW50YW50OiBbXG4gICAgICAvLyBBY2NvdW50aW5nIG9wZXJhdGlvbnMgb25seSAtIGNhbm5vdCBhcHByb3ZlIGV4cGVuc2VzIG9yIG1hbmFnZSB1c2Vyc1xuICAgICAgJ2ludm9pY2VzLmNyZWF0ZScsICdpbnZvaWNlcy5yZWFkJywgJ2ludm9pY2VzLnVwZGF0ZScsXG4gICAgICAnZXhwZW5zZXMuY3JlYXRlJywgJ2V4cGVuc2VzLnJlYWQnLFxuICAgICAgJ3BheXJvbGwucmVhZCcsXG4gICAgICAncmVwb3J0cy5yZWFkJyxcbiAgICAgICdjdXN0b21lcnMucmVhZCcsICdjdXN0b21lcnMudXBkYXRlJyxcbiAgICAgICdzdXBwbGllcnMucmVhZCcsICdzdXBwbGllcnMudXBkYXRlJyxcbiAgICAgICdkYXNoYm9hcmQucmVhZCdcbiAgICBdLFxuICAgIGF1ZGl0b3I6IFtcbiAgICAgIC8vIFJlYWQtb25seSBhY2Nlc3MgZm9yIGF1ZGl0aW5nIHB1cnBvc2VzXG4gICAgICAnaW52b2ljZXMucmVhZCcsXG4gICAgICAnZXhwZW5zZXMucmVhZCcsXG4gICAgICAncGF5cm9sbC5yZWFkJyxcbiAgICAgICdyZXBvcnRzLnJlYWQnLFxuICAgICAgJ2F1ZGl0LnJlYWQnLFxuICAgICAgJ2N1c3RvbWVycy5yZWFkJyxcbiAgICAgICdzdXBwbGllcnMucmVhZCcsXG4gICAgICAnZW1wbG95ZWVzLnJlYWQnLFxuICAgICAgJ2Rhc2hib2FyZC5yZWFkJ1xuICAgIF0sXG4gICAgZW1wbG95ZWU6IFtcbiAgICAgIC8vIFZlcnkgbGltaXRlZCBhY2Nlc3MgLSBvbmx5IG93biBleHBlbnNlcyBhbmQgcGF5cm9sbFxuICAgICAgJ2V4cGVuc2VzLmNyZWF0ZScsXG4gICAgICAnZXhwZW5zZXMucmVhZC5vd24nLFxuICAgICAgJ3BheXJvbGwucmVhZC5vd24nXG4gICAgXVxuICB9LFxuXG4gIC8vIENoZWNrIGlmIHVzZXIgaGFzIHBlcm1pc3Npb24gd2l0aCBzdHJpY3QgYWRtaW4tb25seSByZXN0cmljdGlvbnNcbiAgaGFzUGVybWlzc2lvbih1c2VyUm9sZSwgcGVybWlzc2lvbikge1xuICAgIC8vIE9ubHkgYWRtaW4gY2FuIGFjY2VzcyBjZXJ0YWluIGNyaXRpY2FsIGZ1bmN0aW9uc1xuICAgIGNvbnN0IGFkbWluT25seVBlcm1pc3Npb25zID0gW1xuICAgICAgJ3VzZXJzLmNyZWF0ZScsICd1c2Vycy51cGRhdGUnLCAndXNlcnMuZGVsZXRlJyxcbiAgICAgICdjb21wYW5pZXMuY3JlYXRlJywgJ2NvbXBhbmllcy51cGRhdGUnLCAnY29tcGFuaWVzLmRlbGV0ZScsXG4gICAgICAnc2V0dGluZ3MucmVhZCcsICdzZXR0aW5ncy51cGRhdGUnLFxuICAgICAgJ2F1ZGl0LnJlYWQnLCAnYXVkaXQuZXhwb3J0JyxcbiAgICAgICdkYXNoYm9hcmQuYWRtaW4nXG4gICAgXTtcblxuICAgIGlmIChhZG1pbk9ubHlQZXJtaXNzaW9ucy5pbmNsdWRlcyhwZXJtaXNzaW9uKSAmJiB1c2VyUm9sZSAhPT0gJ2FkbWluJykge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIGNvbnN0IHBlcm1pc3Npb25zID0gdGhpcy5yb2xlUGVybWlzc2lvbnNbdXNlclJvbGVdIHx8IFtdO1xuICAgIHJldHVybiBwZXJtaXNzaW9ucy5pbmNsdWRlcyhwZXJtaXNzaW9uKTtcbiAgfSxcblxuICAvLyBDaGVjayBtdWx0aXBsZSBwZXJtaXNzaW9uc1xuICBoYXNBbnlQZXJtaXNzaW9uKHVzZXJSb2xlLCBwZXJtaXNzaW9ucykge1xuICAgIHJldHVybiBwZXJtaXNzaW9ucy5zb21lKHBlcm1pc3Npb24gPT4gdGhpcy5oYXNQZXJtaXNzaW9uKHVzZXJSb2xlLCBwZXJtaXNzaW9uKSk7XG4gIH0sXG5cbiAgLy8gQ2hlY2sgYWxsIHBlcm1pc3Npb25zXG4gIGhhc0FsbFBlcm1pc3Npb25zKHVzZXJSb2xlLCBwZXJtaXNzaW9ucykge1xuICAgIHJldHVybiBwZXJtaXNzaW9ucy5ldmVyeShwZXJtaXNzaW9uID0+IHRoaXMuaGFzUGVybWlzc2lvbih1c2VyUm9sZSwgcGVybWlzc2lvbikpO1xuICB9LFxuXG4gIC8vIENoZWNrIGlmIHVzZXIgY2FuIGFjY2VzcyBzcGVjaWZpYyByZXNvdXJjZSAoZm9yIG93biBkYXRhIHJlc3RyaWN0aW9ucylcbiAgY2FuQWNjZXNzUmVzb3VyY2UodXNlclJvbGUsIHVzZXJJZCwgcmVzb3VyY2VVc2VySWQsIHBlcm1pc3Npb24pIHtcbiAgICAvLyBBZG1pbiBjYW4gYWNjZXNzIGV2ZXJ5dGhpbmdcbiAgICBpZiAodXNlclJvbGUgPT09ICdhZG1pbicpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIC8vIEZvciBcIm93blwiIHBlcm1pc3Npb25zLCBjaGVjayBpZiB1c2VyIGlzIGFjY2Vzc2luZyB0aGVpciBvd24gZGF0YVxuICAgIGlmIChwZXJtaXNzaW9uLmVuZHNXaXRoKCcub3duJykpIHtcbiAgICAgIHJldHVybiB1c2VySWQgPT09IHJlc291cmNlVXNlcklkO1xuICAgIH1cblxuICAgIC8vIFJlZ3VsYXIgcGVybWlzc2lvbiBjaGVja1xuICAgIHJldHVybiB0aGlzLmhhc1Blcm1pc3Npb24odXNlclJvbGUsIHBlcm1pc3Npb24pO1xuICB9LFxuXG4gIC8vIEdldCB1c2VyIGxpbWl0YXRpb25zIGJhc2VkIG9uIHJvbGVcbiAgZ2V0VXNlckxpbWl0YXRpb25zKHVzZXJSb2xlKSB7XG4gICAgY29uc3QgbGltaXRhdGlvbnMgPSB7XG4gICAgICBhZG1pbjoge1xuICAgICAgICBjYW5NYW5hZ2VVc2VyczogdHJ1ZSxcbiAgICAgICAgY2FuTWFuYWdlU2V0dGluZ3M6IHRydWUsXG4gICAgICAgIGNhblZpZXdBdWRpdFRyYWlsOiB0cnVlLFxuICAgICAgICBjYW5BcHByb3ZlRXhwZW5zZXM6IHRydWUsXG4gICAgICAgIGNhbkRlbGV0ZVJlY29yZHM6IHRydWUsXG4gICAgICAgIGNhbkV4cG9ydERhdGE6IHRydWUsXG4gICAgICAgIGNhbkFjY2Vzc0FsbERhdGE6IHRydWUsXG4gICAgICAgIG1heFJlY29yZHNQZXJQYWdlOiAxMDAwXG4gICAgICB9LFxuICAgICAgbWFuYWdlcjoge1xuICAgICAgICBjYW5NYW5hZ2VVc2VyczogZmFsc2UsXG4gICAgICAgIGNhbk1hbmFnZVNldHRpbmdzOiBmYWxzZSxcbiAgICAgICAgY2FuVmlld0F1ZGl0VHJhaWw6IGZhbHNlLFxuICAgICAgICBjYW5BcHByb3ZlRXhwZW5zZXM6IHRydWUsXG4gICAgICAgIGNhbkRlbGV0ZVJlY29yZHM6IGZhbHNlLFxuICAgICAgICBjYW5FeHBvcnREYXRhOiBmYWxzZSxcbiAgICAgICAgY2FuQWNjZXNzQWxsRGF0YTogdHJ1ZSxcbiAgICAgICAgbWF4UmVjb3Jkc1BlclBhZ2U6IDUwMFxuICAgICAgfSxcbiAgICAgIGFjY291bnRhbnQ6IHtcbiAgICAgICAgY2FuTWFuYWdlVXNlcnM6IGZhbHNlLFxuICAgICAgICBjYW5NYW5hZ2VTZXR0aW5nczogZmFsc2UsXG4gICAgICAgIGNhblZpZXdBdWRpdFRyYWlsOiBmYWxzZSxcbiAgICAgICAgY2FuQXBwcm92ZUV4cGVuc2VzOiBmYWxzZSxcbiAgICAgICAgY2FuRGVsZXRlUmVjb3JkczogZmFsc2UsXG4gICAgICAgIGNhbkV4cG9ydERhdGE6IGZhbHNlLFxuICAgICAgICBjYW5BY2Nlc3NBbGxEYXRhOiB0cnVlLFxuICAgICAgICBtYXhSZWNvcmRzUGVyUGFnZTogMjAwXG4gICAgICB9LFxuICAgICAgYXVkaXRvcjoge1xuICAgICAgICBjYW5NYW5hZ2VVc2VyczogZmFsc2UsXG4gICAgICAgIGNhbk1hbmFnZVNldHRpbmdzOiBmYWxzZSxcbiAgICAgICAgY2FuVmlld0F1ZGl0VHJhaWw6IHRydWUsXG4gICAgICAgIGNhbkFwcHJvdmVFeHBlbnNlczogZmFsc2UsXG4gICAgICAgIGNhbkRlbGV0ZVJlY29yZHM6IGZhbHNlLFxuICAgICAgICBjYW5FeHBvcnREYXRhOiB0cnVlLFxuICAgICAgICBjYW5BY2Nlc3NBbGxEYXRhOiB0cnVlLFxuICAgICAgICBtYXhSZWNvcmRzUGVyUGFnZTogMTAwLFxuICAgICAgICByZWFkT25seUFjY2VzczogdHJ1ZVxuICAgICAgfSxcbiAgICAgIGVtcGxveWVlOiB7XG4gICAgICAgIGNhbk1hbmFnZVVzZXJzOiBmYWxzZSxcbiAgICAgICAgY2FuTWFuYWdlU2V0dGluZ3M6IGZhbHNlLFxuICAgICAgICBjYW5WaWV3QXVkaXRUcmFpbDogZmFsc2UsXG4gICAgICAgIGNhbkFwcHJvdmVFeHBlbnNlczogZmFsc2UsXG4gICAgICAgIGNhbkRlbGV0ZVJlY29yZHM6IGZhbHNlLFxuICAgICAgICBjYW5FeHBvcnREYXRhOiBmYWxzZSxcbiAgICAgICAgY2FuQWNjZXNzQWxsRGF0YTogZmFsc2UsXG4gICAgICAgIG1heFJlY29yZHNQZXJQYWdlOiA1MCxcbiAgICAgICAgb3duRGF0YU9ubHk6IHRydWVcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgcmV0dXJuIGxpbWl0YXRpb25zW3VzZXJSb2xlXSB8fCBsaW1pdGF0aW9ucy5lbXBsb3llZTtcbiAgfVxufTtcblxuLy8gU2Vzc2lvbiBtYW5hZ2VtZW50XG5leHBvcnQgY29uc3Qgc2Vzc2lvblV0aWxzID0ge1xuICAvLyBDcmVhdGUgc2Vzc2lvbiBkYXRhXG4gIGNyZWF0ZVNlc3Npb24odXNlcikge1xuICAgIHJldHVybiB7XG4gICAgICBpZDogdXNlci5pZCxcbiAgICAgIHVzZXJuYW1lOiB1c2VyLnVzZXJuYW1lLFxuICAgICAgZW1haWw6IHVzZXIuZW1haWwsXG4gICAgICByb2xlOiB1c2VyLnJvbGUsXG4gICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3RfbmFtZSxcbiAgICAgIGxhc3ROYW1lOiB1c2VyLmxhc3RfbmFtZSxcbiAgICAgIGlzQWN0aXZlOiB1c2VyLmlzX2FjdGl2ZSxcbiAgICAgIG1mYUVuYWJsZWQ6IHVzZXIubWZhX2VuYWJsZWQsXG4gICAgICBsb2dpblRpbWU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH07XG4gIH0sXG5cbiAgLy8gVmFsaWRhdGUgc2Vzc2lvblxuICB2YWxpZGF0ZVNlc3Npb24oc2Vzc2lvbikge1xuICAgIGlmICghc2Vzc2lvbiB8fCAhc2Vzc2lvbi5pZCB8fCAhc2Vzc2lvbi5yb2xlKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIFxuICAgIC8vIENoZWNrIGlmIHNlc3Npb24gaXMgZXhwaXJlZCAoMjQgaG91cnMpXG4gICAgY29uc3QgbG9naW5UaW1lID0gbmV3IERhdGUoc2Vzc2lvbi5sb2dpblRpbWUpO1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgaG91cnNEaWZmID0gKG5vdyAtIGxvZ2luVGltZSkgLyAoMTAwMCAqIDYwICogNjApO1xuICAgIFxuICAgIHJldHVybiBob3Vyc0RpZmYgPCAyNDtcbiAgfVxufTtcblxuLy8gU2VjdXJpdHkgdXRpbGl0aWVzXG5leHBvcnQgY29uc3Qgc2VjdXJpdHlVdGlscyA9IHtcbiAgLy8gR2VuZXJhdGUgc2VjdXJlIHJhbmRvbSBzdHJpbmdcbiAgZ2VuZXJhdGVTZWN1cmVSYW5kb20obGVuZ3RoID0gMzIpIHtcbiAgICBjb25zdCBjaGFycyA9ICdBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWjAxMjM0NTY3ODlhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5eic7XG4gICAgbGV0IHJlc3VsdCA9ICcnO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgIHJlc3VsdCArPSBjaGFycy5jaGFyQXQoTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogY2hhcnMubGVuZ3RoKSk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG4gIH0sXG5cbiAgLy8gVmFsaWRhdGUgSVAgYWRkcmVzc1xuICBpc1ZhbGlkSVAoaXApIHtcbiAgICBjb25zdCBpcHY0UmVnZXggPSAvXig/Oig/OjI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcXC4pezN9KD86MjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KSQvO1xuICAgIGNvbnN0IGlwdjZSZWdleCA9IC9eKD86WzAtOWEtZkEtRl17MSw0fTopezd9WzAtOWEtZkEtRl17MSw0fSQvO1xuICAgIHJldHVybiBpcHY0UmVnZXgudGVzdChpcCkgfHwgaXB2NlJlZ2V4LnRlc3QoaXApO1xuICB9LFxuXG4gIC8vIFNhbml0aXplIGlucHV0XG4gIHNhbml0aXplSW5wdXQoaW5wdXQpIHtcbiAgICBpZiAodHlwZW9mIGlucHV0ICE9PSAnc3RyaW5nJykgcmV0dXJuIGlucHV0O1xuICAgIHJldHVybiBpbnB1dC5yZXBsYWNlKC88c2NyaXB0XFxiW148XSooPzooPyE8XFwvc2NyaXB0Pik8W148XSopKjxcXC9zY3JpcHQ+L2dpLCAnJylcbiAgICAgICAgICAgICAgICAucmVwbGFjZSgvamF2YXNjcmlwdDovZ2ksICcnKVxuICAgICAgICAgICAgICAgIC5yZXBsYWNlKC9vblxcdytcXHMqPS9naSwgJycpO1xuICB9LFxuXG4gIC8vIFJhdGUgbGltaXRpbmcgaGVscGVyXG4gIGNyZWF0ZVJhdGVMaW1pdGVyKHdpbmRvd01zID0gMTUgKiA2MCAqIDEwMDAsIG1heCA9IDEwMCkge1xuICAgIGNvbnN0IHJlcXVlc3RzID0gbmV3IE1hcCgpO1xuICAgIFxuICAgIHJldHVybiAoaWRlbnRpZmllcikgPT4ge1xuICAgICAgY29uc3Qgbm93ID0gRGF0ZS5ub3coKTtcbiAgICAgIGNvbnN0IHdpbmRvd1N0YXJ0ID0gbm93IC0gd2luZG93TXM7XG4gICAgICBcbiAgICAgIC8vIENsZWFuIG9sZCBlbnRyaWVzXG4gICAgICBmb3IgKGNvbnN0IFtrZXksIHRpbWVzdGFtcHNdIG9mIHJlcXVlc3RzLmVudHJpZXMoKSkge1xuICAgICAgICBjb25zdCB2YWxpZFRpbWVzdGFtcHMgPSB0aW1lc3RhbXBzLmZpbHRlcih0aW1lID0+IHRpbWUgPiB3aW5kb3dTdGFydCk7XG4gICAgICAgIGlmICh2YWxpZFRpbWVzdGFtcHMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgcmVxdWVzdHMuZGVsZXRlKGtleSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmVxdWVzdHMuc2V0KGtleSwgdmFsaWRUaW1lc3RhbXBzKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBDaGVjayBjdXJyZW50IGlkZW50aWZpZXJcbiAgICAgIGNvbnN0IHVzZXJSZXF1ZXN0cyA9IHJlcXVlc3RzLmdldChpZGVudGlmaWVyKSB8fCBbXTtcbiAgICAgIGNvbnN0IHJlY2VudFJlcXVlc3RzID0gdXNlclJlcXVlc3RzLmZpbHRlcih0aW1lID0+IHRpbWUgPiB3aW5kb3dTdGFydCk7XG4gICAgICBcbiAgICAgIGlmIChyZWNlbnRSZXF1ZXN0cy5sZW5ndGggPj0gbWF4KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTsgLy8gUmF0ZSBsaW1pdCBleGNlZWRlZFxuICAgICAgfVxuICAgICAgXG4gICAgICByZWNlbnRSZXF1ZXN0cy5wdXNoKG5vdyk7XG4gICAgICByZXF1ZXN0cy5zZXQoaWRlbnRpZmllciwgcmVjZW50UmVxdWVzdHMpO1xuICAgICAgcmV0dXJuIHRydWU7IC8vIFJlcXVlc3QgYWxsb3dlZFxuICAgIH07XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgcGFzc3dvcmRVdGlscyxcbiAgand0VXRpbHMsXG4gIG1mYVV0aWxzLFxuICBlbmNyeXB0aW9uVXRpbHMsXG4gIHJiYWNVdGlscyxcbiAgc2Vzc2lvblV0aWxzLFxuICBzZWN1cml0eVV0aWxzXG59O1xuIl0sIm5hbWVzIjpbImp3dCIsImJjcnlwdCIsInNwZWFrZWFzeSIsIlFSQ29kZSIsIkNyeXB0b0pTIiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJFTkNSWVBUSU9OX0tFWSIsInBhc3N3b3JkVXRpbHMiLCJoYXNoUGFzc3dvcmQiLCJwYXNzd29yZCIsInNhbHRSb3VuZHMiLCJoYXNoIiwidmVyaWZ5UGFzc3dvcmQiLCJjb21wYXJlIiwiZ2VuZXJhdGVTZWN1cmVQYXNzd29yZCIsImxlbmd0aCIsImNoYXJzZXQiLCJpIiwiY2hhckF0IiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwiand0VXRpbHMiLCJnZW5lcmF0ZVRva2VuIiwicGF5bG9hZCIsImV4cGlyZXNJbiIsInNpZ24iLCJ2ZXJpZnlUb2tlbiIsInRva2VuIiwidmVyaWZ5IiwiZXJyb3IiLCJFcnJvciIsImdlbmVyYXRlUmVmcmVzaFRva2VuIiwibWZhVXRpbHMiLCJnZW5lcmF0ZVNlY3JldCIsInVzZXJFbWFpbCIsIm5hbWUiLCJpc3N1ZXIiLCJnZW5lcmF0ZVFSQ29kZSIsInNlY3JldCIsInRvRGF0YVVSTCIsIm90cGF1dGhfdXJsIiwidG90cCIsImVuY29kaW5nIiwid2luZG93IiwiZ2VuZXJhdGVCYWNrdXBDb2RlcyIsImNvdW50IiwiY29kZXMiLCJjb2RlIiwidG9TdHJpbmciLCJzdWJzdHJpbmciLCJ0b1VwcGVyQ2FzZSIsInB1c2giLCJlbmNyeXB0aW9uVXRpbHMiLCJlbmNyeXB0IiwidGV4dCIsIkFFUyIsImRlY3J5cHQiLCJjaXBoZXJ0ZXh0IiwiYnl0ZXMiLCJlbmMiLCJVdGY4IiwiU0hBMjU2IiwicmJhY1V0aWxzIiwicm9sZVBlcm1pc3Npb25zIiwiYWRtaW4iLCJtYW5hZ2VyIiwiYWNjb3VudGFudCIsImF1ZGl0b3IiLCJlbXBsb3llZSIsImhhc1Blcm1pc3Npb24iLCJ1c2VyUm9sZSIsInBlcm1pc3Npb24iLCJhZG1pbk9ubHlQZXJtaXNzaW9ucyIsImluY2x1ZGVzIiwicGVybWlzc2lvbnMiLCJoYXNBbnlQZXJtaXNzaW9uIiwic29tZSIsImhhc0FsbFBlcm1pc3Npb25zIiwiZXZlcnkiLCJjYW5BY2Nlc3NSZXNvdXJjZSIsInVzZXJJZCIsInJlc291cmNlVXNlcklkIiwiZW5kc1dpdGgiLCJnZXRVc2VyTGltaXRhdGlvbnMiLCJsaW1pdGF0aW9ucyIsImNhbk1hbmFnZVVzZXJzIiwiY2FuTWFuYWdlU2V0dGluZ3MiLCJjYW5WaWV3QXVkaXRUcmFpbCIsImNhbkFwcHJvdmVFeHBlbnNlcyIsImNhbkRlbGV0ZVJlY29yZHMiLCJjYW5FeHBvcnREYXRhIiwiY2FuQWNjZXNzQWxsRGF0YSIsIm1heFJlY29yZHNQZXJQYWdlIiwicmVhZE9ubHlBY2Nlc3MiLCJvd25EYXRhT25seSIsInNlc3Npb25VdGlscyIsImNyZWF0ZVNlc3Npb24iLCJ1c2VyIiwiaWQiLCJ1c2VybmFtZSIsImVtYWlsIiwicm9sZSIsImZpcnN0TmFtZSIsImZpcnN0X25hbWUiLCJsYXN0TmFtZSIsImxhc3RfbmFtZSIsImlzQWN0aXZlIiwiaXNfYWN0aXZlIiwibWZhRW5hYmxlZCIsIm1mYV9lbmFibGVkIiwibG9naW5UaW1lIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidmFsaWRhdGVTZXNzaW9uIiwic2Vzc2lvbiIsIm5vdyIsImhvdXJzRGlmZiIsInNlY3VyaXR5VXRpbHMiLCJnZW5lcmF0ZVNlY3VyZVJhbmRvbSIsImNoYXJzIiwicmVzdWx0IiwiaXNWYWxpZElQIiwiaXAiLCJpcHY0UmVnZXgiLCJpcHY2UmVnZXgiLCJ0ZXN0Iiwic2FuaXRpemVJbnB1dCIsImlucHV0IiwicmVwbGFjZSIsImNyZWF0ZVJhdGVMaW1pdGVyIiwid2luZG93TXMiLCJtYXgiLCJyZXF1ZXN0cyIsIk1hcCIsImlkZW50aWZpZXIiLCJ3aW5kb3dTdGFydCIsImtleSIsInRpbWVzdGFtcHMiLCJlbnRyaWVzIiwidmFsaWRUaW1lc3RhbXBzIiwiZmlsdGVyIiwidGltZSIsImRlbGV0ZSIsInNldCIsInVzZXJSZXF1ZXN0cyIsImdldCIsInJlY2VudFJlcXVlc3RzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./lib/auth.js\n");

/***/ }),

/***/ "(api)/./lib/db.js":
/*!*******************!*\
  !*** ./lib/db.js ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dbUtils: () => (/* binding */ dbUtils),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   executeQuery: () => (/* binding */ executeQuery),\n/* harmony export */   executeTransaction: () => (/* binding */ executeTransaction),\n/* harmony export */   getPool: () => (/* binding */ getPool)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n// Database connection and utilities\n\n// Database configuration\nconst dbConfig = {\n    host: \"localhost\" || 0,\n    user: \"root\" || 0,\n    password:  false || \"\",\n    database: \"enterprise_accounting\" || 0,\n    waitForConnections: true,\n    connectionLimit: 10,\n    queueLimit: 0,\n    acquireTimeout: 60000,\n    timeout: 60000,\n    reconnect: true\n};\n// Create connection pool\nlet pool;\nconst getPool = ()=>{\n    if (!pool) {\n        pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n    }\n    return pool;\n};\n// Execute query with error handling\nconst executeQuery = async (query, params = [])=>{\n    try {\n        const connection = getPool();\n        const [results] = await connection.execute(query, params);\n        return results;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw new Error(\"Database operation failed\");\n    }\n};\n// Execute transaction\nconst executeTransaction = async (queries)=>{\n    const connection = await getPool().getConnection();\n    try {\n        await connection.beginTransaction();\n        const results = [];\n        for (const { query, params } of queries){\n            const [result] = await connection.execute(query, params);\n            results.push(result);\n        }\n        await connection.commit();\n        return results;\n    } catch (error) {\n        await connection.rollback();\n        console.error(\"Transaction error:\", error);\n        throw new Error(\"Transaction failed\");\n    } finally{\n        connection.release();\n    }\n};\n// Database utility functions\nconst dbUtils = {\n    // Get user by email or username\n    async getUserByCredentials (credential) {\n        const query = `\n      SELECT id, username, email, password_hash, role, first_name, last_name, \n             is_active, mfa_enabled, mfa_secret\n      FROM users \n      WHERE (email = ? OR username = ?) AND is_active = TRUE\n    `;\n        const results = await executeQuery(query, [\n            credential,\n            credential\n        ]);\n        return results[0] || null;\n    },\n    // Create new user\n    async createUser (userData) {\n        const query = `\n      INSERT INTO users (username, email, password_hash, role, first_name, last_name, phone)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            userData.username,\n            userData.email,\n            userData.password_hash,\n            userData.role || \"employee\",\n            userData.first_name,\n            userData.last_name,\n            userData.phone || null\n        ];\n        return await executeQuery(query, params);\n    },\n    // Update user last login\n    async updateLastLogin (userId) {\n        const query = \"UPDATE users SET last_login = NOW() WHERE id = ?\";\n        return await executeQuery(query, [\n            userId\n        ]);\n    },\n    // Get company by ID\n    async getCompanyById (companyId) {\n        const query = \"SELECT * FROM companies WHERE id = ?\";\n        const results = await executeQuery(query, [\n            companyId\n        ]);\n        return results[0] || null;\n    },\n    // Get chart of accounts\n    async getChartOfAccounts (companyId) {\n        const query = `\n      SELECT id, account_code, account_name, account_type, parent_account_id, is_active\n      FROM chart_of_accounts \n      WHERE company_id = ? AND is_active = TRUE\n      ORDER BY account_code\n    `;\n        return await executeQuery(query, [\n            companyId\n        ]);\n    },\n    // Create general ledger entry\n    async createLedgerEntry (entryData) {\n        const query = `\n      INSERT INTO general_ledger \n      (company_id, account_id, transaction_date, description, reference_number, \n       debit_amount, credit_amount, balance, created_by)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            entryData.company_id,\n            entryData.account_id,\n            entryData.transaction_date,\n            entryData.description,\n            entryData.reference_number,\n            entryData.debit_amount || 0,\n            entryData.credit_amount || 0,\n            entryData.balance || 0,\n            entryData.created_by\n        ];\n        return await executeQuery(query, params);\n    },\n    // Get customers\n    async getCustomers (companyId, limit = 50, offset = 0) {\n        const query = `\n      SELECT id, customer_code, name, email, phone, address, city, country, \n             credit_limit, payment_terms, is_active\n      FROM customers \n      WHERE company_id = ? AND is_active = TRUE\n      ORDER BY name\n      LIMIT ? OFFSET ?\n    `;\n        return await executeQuery(query, [\n            companyId,\n            limit,\n            offset\n        ]);\n    },\n    // Create customer\n    async createCustomer (customerData) {\n        const query = `\n      INSERT INTO customers \n      (company_id, customer_code, name, email, phone, address, city, country, \n       tax_number, credit_limit, payment_terms)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            customerData.company_id,\n            customerData.customer_code,\n            customerData.name,\n            customerData.email,\n            customerData.phone,\n            customerData.address,\n            customerData.city,\n            customerData.country,\n            customerData.tax_number,\n            customerData.credit_limit || 0,\n            customerData.payment_terms || 30\n        ];\n        return await executeQuery(query, params);\n    },\n    // Get invoices\n    async getInvoices (companyId, limit = 50, offset = 0) {\n        const query = `\n      SELECT i.*, c.name as customer_name\n      FROM invoices i\n      JOIN customers c ON i.customer_id = c.id\n      WHERE i.company_id = ?\n      ORDER BY i.invoice_date DESC\n      LIMIT ? OFFSET ?\n    `;\n        return await executeQuery(query, [\n            companyId,\n            limit,\n            offset\n        ]);\n    },\n    // Create invoice\n    async createInvoice (invoiceData) {\n        const query = `\n      INSERT INTO invoices \n      (company_id, customer_id, invoice_number, invoice_date, due_date, \n       subtotal, tax_amount, discount_amount, total_amount, notes, created_by)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            invoiceData.company_id,\n            invoiceData.customer_id,\n            invoiceData.invoice_number,\n            invoiceData.invoice_date,\n            invoiceData.due_date,\n            invoiceData.subtotal,\n            invoiceData.tax_amount || 0,\n            invoiceData.discount_amount || 0,\n            invoiceData.total_amount,\n            invoiceData.notes,\n            invoiceData.created_by\n        ];\n        return await executeQuery(query, params);\n    },\n    // Get expenses\n    async getExpenses (companyId, limit = 50, offset = 0) {\n        const query = `\n      SELECT e.*, s.name as supplier_name, u.first_name, u.last_name\n      FROM expenses e\n      LEFT JOIN suppliers s ON e.supplier_id = s.id\n      JOIN users u ON e.created_by = u.id\n      WHERE e.company_id = ?\n      ORDER BY e.expense_date DESC\n      LIMIT ? OFFSET ?\n    `;\n        return await executeQuery(query, [\n            companyId,\n            limit,\n            offset\n        ]);\n    },\n    // Create expense\n    async createExpense (expenseData) {\n        const query = `\n      INSERT INTO expenses \n      (company_id, supplier_id, expense_number, expense_date, category, \n       description, amount, tax_amount, total_amount, receipt_path, created_by)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            expenseData.company_id,\n            expenseData.supplier_id,\n            expenseData.expense_number,\n            expenseData.expense_date,\n            expenseData.category,\n            expenseData.description,\n            expenseData.amount,\n            expenseData.tax_amount || 0,\n            expenseData.total_amount,\n            expenseData.receipt_path,\n            expenseData.created_by\n        ];\n        return await executeQuery(query, params);\n    },\n    // Get employees\n    async getEmployees (companyId, limit = 50, offset = 0) {\n        const query = `\n      SELECT id, employee_code, first_name, last_name, email, phone, \n             position, department, hire_date, salary, is_active\n      FROM employees \n      WHERE company_id = ? AND is_active = TRUE\n      ORDER BY first_name, last_name\n      LIMIT ? OFFSET ?\n    `;\n        return await executeQuery(query, [\n            companyId,\n            limit,\n            offset\n        ]);\n    },\n    // Create audit trail entry\n    async createAuditEntry (auditData) {\n        const query = `\n      INSERT INTO audit_trail\n      (user_id, table_name, record_id, action, old_values, new_values, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n    `;\n        const params = [\n            auditData.user_id,\n            auditData.table_name,\n            auditData.record_id,\n            auditData.action,\n            JSON.stringify(auditData.old_values),\n            JSON.stringify(auditData.new_values),\n            auditData.ip_address,\n            auditData.user_agent\n        ];\n        return await executeQuery(query, params);\n    },\n    // Execute query helper (for direct access)\n    async executeQuery (query, params = []) {\n        return await executeQuery(query, params);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbUtils);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/db.js\n");

/***/ }),

/***/ "(api)/./pages/api/customers/index.js":
/*!**************************************!*\
  !*** ./pages/api/customers/index.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/db */ \"(api)/./lib/db.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/auth */ \"(api)/./lib/auth.js\");\n// Customers API endpoint\n\n\nasync function handler(req, res) {\n    try {\n        // Verify authentication\n        const authHeader = req.headers.authorization;\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return res.status(401).json({\n                error: \"No token provided\"\n            });\n        }\n        const token = authHeader.substring(7);\n        const decoded = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.jwtUtils.verifyToken(token);\n        const companyId = 1; // Default company - in real app, get from user context\n        if (req.method === \"GET\") {\n            // Check read permission\n            if (!_lib_auth__WEBPACK_IMPORTED_MODULE_1__.rbacUtils.hasPermission(decoded.role, \"customers.read\")) {\n                return res.status(403).json({\n                    error: \"Insufficient permissions\"\n                });\n            }\n            const { page = 1, limit = 10, search = \"\", status = \"all\" } = req.query;\n            const offset = (page - 1) * limit;\n            // Build search query\n            let whereClause = \"WHERE c.company_id = ?\";\n            let params = [\n                companyId\n            ];\n            if (search) {\n                whereClause += \" AND (c.name LIKE ? OR c.email LIKE ? OR c.customer_code LIKE ?)\";\n                const searchTerm = `%${search}%`;\n                params.push(searchTerm, searchTerm, searchTerm);\n            }\n            if (status !== \"all\") {\n                whereClause += \" AND c.is_active = ?\";\n                params.push(status === \"active\" ? 1 : 0);\n            }\n            // Get customers with pagination\n            const customersQuery = `\n        SELECT c.*, \n               COUNT(i.id) as total_invoices,\n               COALESCE(SUM(i.total_amount), 0) as total_billed,\n               COALESCE(SUM(i.paid_amount), 0) as total_paid\n        FROM customers c\n        LEFT JOIN invoices i ON c.id = i.customer_id\n        ${whereClause}\n        GROUP BY c.id\n        ORDER BY c.created_at DESC\n        LIMIT ? OFFSET ?\n      `;\n            const customers = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.dbUtils.executeQuery(customersQuery, [\n                ...params,\n                parseInt(limit),\n                offset\n            ]);\n            // Get total count\n            const countQuery = `\n        SELECT COUNT(*) as total\n        FROM customers c\n        ${whereClause}\n      `;\n            const countResult = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.dbUtils.executeQuery(countQuery, params);\n            const total = countResult[0].total;\n            res.status(200).json({\n                success: true,\n                data: {\n                    customers,\n                    pagination: {\n                        page: parseInt(page),\n                        limit: parseInt(limit),\n                        total,\n                        pages: Math.ceil(total / limit)\n                    }\n                }\n            });\n        } else if (req.method === \"POST\") {\n            // Check create permission\n            if (!_lib_auth__WEBPACK_IMPORTED_MODULE_1__.rbacUtils.hasPermission(decoded.role, \"customers.create\")) {\n                return res.status(403).json({\n                    error: \"Insufficient permissions\"\n                });\n            }\n            const { name, email, phone, address, city, country, tax_number, credit_limit = 0, payment_terms = 30 } = req.body;\n            // Validate required fields\n            if (!name) {\n                return res.status(400).json({\n                    error: \"Customer name is required\"\n                });\n            }\n            // Generate customer code\n            const customerCode = await generateCustomerCode(companyId);\n            // Insert customer\n            const insertQuery = `\n        INSERT INTO customers \n        (company_id, customer_code, name, email, phone, address, city, country, tax_number, credit_limit, payment_terms)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      `;\n            const result = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.dbUtils.executeQuery(insertQuery, [\n                companyId,\n                customerCode,\n                name,\n                email,\n                phone,\n                address,\n                city,\n                country,\n                tax_number,\n                credit_limit,\n                payment_terms\n            ]);\n            const customerId = result.insertId;\n            // Create audit trail\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.dbUtils.createAuditEntry({\n                user_id: decoded.id,\n                table_name: \"customers\",\n                record_id: customerId,\n                action: \"INSERT\",\n                old_values: null,\n                new_values: {\n                    name,\n                    email,\n                    customer_code: customerCode\n                },\n                ip_address: req.headers[\"x-forwarded-for\"] || req.connection.remoteAddress,\n                user_agent: req.headers[\"user-agent\"]\n            });\n            res.status(201).json({\n                success: true,\n                message: \"Customer created successfully\",\n                data: {\n                    id: customerId,\n                    customer_code: customerCode,\n                    name\n                }\n            });\n        } else {\n            res.status(405).json({\n                error: \"Method not allowed\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Customers API error:\", error);\n        res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n// Generate unique customer code\nasync function generateCustomerCode(companyId) {\n    const prefix = \"CUST\";\n    const year = new Date().getFullYear().toString().slice(-2);\n    // Get the last customer number for this year\n    const query = `\n    SELECT customer_code \n    FROM customers \n    WHERE company_id = ? AND customer_code LIKE ?\n    ORDER BY customer_code DESC \n    LIMIT 1\n  `;\n    const pattern = `${prefix}${year}%`;\n    const result = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.dbUtils.executeQuery(query, [\n        companyId,\n        pattern\n    ]);\n    let nextNumber = 1;\n    if (result.length > 0) {\n        const lastCode = result[0].customer_code;\n        const lastNumber = parseInt(lastCode.slice(-4));\n        nextNumber = lastNumber + 1;\n    }\n    return `${prefix}${year}${nextNumber.toString().padStart(4, \"0\")}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/customers/index.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcustomers&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccustomers%5Cindex.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();