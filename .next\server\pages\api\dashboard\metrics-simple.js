"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/dashboard/metrics-simple";
exports.ids = ["pages/api/dashboard/metrics-simple"];
exports.modules = {

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard%2Fmetrics-simple&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard%5Cmetrics-simple.js&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard%2Fmetrics-simple&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard%5Cmetrics-simple.js&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_dashboard_metrics_simple_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\dashboard\\metrics-simple.js */ \"(api)/./pages/api/dashboard/metrics-simple.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_metrics_simple_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_dashboard_metrics_simple_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/dashboard/metrics-simple\",\n        pathname: \"/api/dashboard/metrics-simple\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_dashboard_metrics_simple_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard%2Fmetrics-simple&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard%5Cmetrics-simple.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/dashboard/metrics-simple.js":
/*!***********************************************!*\
  !*** ./pages/api/dashboard/metrics-simple.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simplified dashboard metrics API\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    try {\n        // Verify authentication\n        const authHeader = req.headers.authorization;\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return res.status(401).json({\n                error: \"No token provided\"\n            });\n        }\n        const token = authHeader.substring(7);\n        const jwtSecret = \"your-super-secret-jwt-key-change-in-production-min-32-chars\" || 0;\n        try {\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n            // Return demo metrics data\n            const metrics = {\n                totalRevenue: 125000,\n                totalExpenses: 85000,\n                netProfit: 40000,\n                pendingInvoices: 12,\n                overdueCount: 3,\n                overdueAmount: 15000,\n                revenueChange: 12.5,\n                expensesChange: 8.2,\n                profitChange: 15.3,\n                period: {\n                    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split(\"T\")[0],\n                    endDate: new Date().toISOString().split(\"T\")[0]\n                }\n            };\n            res.status(200).json({\n                success: true,\n                data: metrics\n            });\n        } catch (jwtError) {\n            return res.status(401).json({\n                error: \"Invalid token\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Dashboard metrics error:\", error);\n        res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/dashboard/metrics-simple.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdashboard%2Fmetrics-simple&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdashboard%5Cmetrics-simple.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();