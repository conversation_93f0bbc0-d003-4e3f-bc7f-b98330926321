/* Enterprise Accounting Software - Main Styles */

/* CSS Variables for theming */
:root {
  /* Primary Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  
  /* Secondary Colors */
  --secondary-color: #64748b;
  --secondary-dark: #475569;
  --secondary-light: #94a3b8;
  
  /* Success, Warning, Error */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #06b6d4;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Typography */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Dark theme variables */
[data-theme="dark"] {
  --white: #0f172a;
  --gray-50: #1e293b;
  --gray-100: #334155;
  --gray-200: #475569;
  --gray-300: #64748b;
  --gray-400: #94a3b8;
  --gray-500: #cbd5e1;
  --gray-600: #e2e8f0;
  --gray-700: #f1f5f9;
  --gray-800: #f8fafc;
  --gray-900: #ffffff;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin-bottom: var(--spacing-4);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Login Page Styles */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.login-container {
  width: 100%;
  max-width: 450px;
}

.login-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-6);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.logo i {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
}

.logo h1 {
  color: var(--gray-900);
  margin: 0;
}

.subtitle {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

input, select, textarea {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  background-color: var(--white);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

input::placeholder {
  color: var(--gray-400);
}

.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-2);
  transition: color var(--transition-fast);
}

.toggle-password:hover {
  color: var(--gray-600);
}

.help-text {
  display: block;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.password-requirements {
  margin-top: var(--spacing-2);
}

.password-requirements small {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

/* Form Options */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px;
  width: 100%;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-login {
  margin-bottom: var(--spacing-4);
}

/* Admin Notice */
.admin-notice {
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  margin: var(--spacing-4) 0;
  color: #92400e;
  font-size: var(--font-size-sm);
  text-align: center;
}

.admin-notice p {
  margin: 0 0 var(--spacing-2) 0;
}

.admin-notice p:last-child {
  margin-bottom: 0;
}

/* Divider */
.divider {
  text-align: center;
  margin: var(--spacing-6) 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--gray-200);
}

.divider span {
  background-color: var(--white);
  padding: 0 var(--spacing-4);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

/* Loading Spinner */
.loading-spinner {
  text-align: center;
  padding: var(--spacing-8);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
}

.alert.success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.alert.error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.alert.warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.alert.info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.alert-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  color: inherit;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* Login Footer */
.login-footer {
  text-align: center;
  color: var(--white);
}

.security-features {
  display: flex;
  justify-content: center;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-4);
  flex-wrap: wrap;
}

.feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.feature i {
  font-size: var(--font-size-base);
}

.copyright {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-page {
  display: flex;
  min-height: 100vh;
  background-color: var(--gray-50);
}

.sidebar {
  width: 280px;
  background-color: var(--white);
  border-right: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 1000;
  transition: transform var(--transition-normal);
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.mobile-hidden {
  transform: translateX(-100%);
}

.main-content {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--transition-normal);
}

.sidebar.collapsed + .main-content {
  margin-left: 70px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-page {
    padding: var(--spacing-2);
  }

  .login-card {
    padding: var(--spacing-6);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .security-features {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .form-options {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }

  .sidebar {
    width: 280px;
    transform: translateX(-100%);
  }

  .sidebar.mobile-visible {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }
}
