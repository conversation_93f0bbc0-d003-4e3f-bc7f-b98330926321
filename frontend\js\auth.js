// Authentication JavaScript Module
class AuthManager {
    constructor() {
        this.apiBaseUrl = '/api';
        this.accessToken = localStorage.getItem('accessToken');
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
    }

    // Show loading spinner
    showLoading() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) spinner.style.display = 'block';
        
        // Disable form buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => btn.disabled = true);
    }

    // Hide loading spinner
    hideLoading() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) spinner.style.display = 'none';
        
        // Enable form buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => btn.disabled = false);
    }

    // Show alert message
    showAlert(message, type = 'info') {
        const alertElement = document.getElementById('alertMessage');
        if (!alertElement) return;

        const alertText = alertElement.querySelector('.alert-text');
        alertText.textContent = message;
        
        // Remove existing type classes
        alertElement.className = 'alert';
        alertElement.classList.add(type);
        alertElement.style.display = 'flex';

        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }
    }

    // Hide alert message
    hideAlert() {
        const alertElement = document.getElementById('alertMessage');
        if (alertElement) alertElement.style.display = 'none';
    }

    // Make API request with authentication
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        // Add authorization header if token exists
        if (this.accessToken) {
            defaultOptions.headers.Authorization = `Bearer ${this.accessToken}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // Login function
    async login(credential, password, mfaToken = null) {
        try {
            this.showLoading();
            this.hideAlert();

            console.log('=== FRONTEND LOGIN ATTEMPT ===');
            console.log('Credential:', credential);
            console.log('Password provided:', password ? 'YES' : 'NO');

            const response = await this.apiRequest('/auth/login-basic', {
                method: 'POST',
                body: JSON.stringify({
                    credential: credential.trim(),
                    password,
                    mfaToken
                }),
            });

            console.log('Login response received:', response);

            if (response.requiresMFA) {
                this.hideLoading();
                this.showMFAInput();
                this.showAlert('Please enter your MFA token to continue', 'info');
                return { requiresMFA: true };
            }

            if (response.success) {
                // Store authentication data
                this.accessToken = response.accessToken;
                this.currentUser = response.user;
                
                localStorage.setItem('accessToken', this.accessToken);
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

                this.showAlert('Login successful! Redirecting...', 'success');
                
                // Redirect to dashboard after short delay
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);

                return response;
            }
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Login failed. Please try again.', 'error');
            throw error;
        }
    }

    // Register function
    async register(userData) {
        try {
            this.showLoading();
            this.hideAlert();

            const response = await this.apiRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData),
            });

            if (response.success) {
                this.hideLoading();
                this.showAlert('Account created successfully! Please login with your credentials.', 'success');
                
                // Switch back to login form after delay
                setTimeout(() => {
                    showLoginForm();
                }, 2000);

                return response;
            }
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Registration failed. Please try again.', 'error');
            throw error;
        }
    }

    // Logout function
    async logout() {
        try {
            if (this.accessToken) {
                await this.apiRequest('/auth/logout', {
                    method: 'POST',
                });
            }
        } catch (error) {
            console.error('Logout API call failed:', error);
        } finally {
            // Clear local storage regardless of API call result
            this.accessToken = null;
            this.currentUser = null;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('currentUser');
            
            // Redirect to login page
            window.location.href = 'index.html';
        }
    }

    // Check if user is authenticated
    isAuthenticated() {
        // Always refresh from localStorage first to ensure we have the latest state
        this.accessToken = localStorage.getItem('accessToken');
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');

        const isAuth = !!(this.accessToken && this.currentUser);
        console.log('🔍 Authentication check:', {
            hasToken: !!this.accessToken,
            hasUser: !!this.currentUser,
            isAuthenticated: isAuth
        });

        return isAuth;
    }

    // Get current user
    getCurrentUser() {
        // Always refresh from localStorage first
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        return this.currentUser;
    }

    // Check user permission with strict admin-only restrictions
    hasPermission(permission) {
        if (!this.currentUser) return false;

        // Only admin has full access
        if (this.currentUser.role === 'admin') return true;

        // Admin-only permissions that no other role can access
        const adminOnlyPermissions = [
            'users.create', 'users.read', 'users.update', 'users.delete',
            'companies.create', 'companies.update', 'companies.delete',
            'settings.read', 'settings.update',
            'audit.read', 'audit.export',
            'dashboard.admin'
        ];

        if (adminOnlyPermissions.includes(permission)) {
            return false; // Non-admin users cannot access these
        }

        // Define limited role permissions for non-admin users
        const rolePermissions = {
            manager: [
                'invoices.read', 'invoices.update',
                'expenses.read', 'expenses.approve',
                'reports.read',
                'dashboard.read'
            ],
            accountant: [
                'invoices.create', 'invoices.read', 'invoices.update',
                'expenses.create', 'expenses.read',
                'payroll.read',
                'reports.read',
                'dashboard.read'
            ],
            auditor: [
                'invoices.read',
                'expenses.read',
                'payroll.read',
                'reports.read',
                'audit.read',
                'dashboard.read'
            ],
            employee: [
                'expenses.create',
                'expenses.read.own',
                'payroll.read.own'
            ]
        };

        const userPermissions = rolePermissions[this.currentUser.role] || [];
        return userPermissions.includes(permission);
    }

    // Check if user is admin
    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }

    // Get user role limitations
    getUserLimitations() {
        if (!this.currentUser) return null;

        const limitations = {
            admin: {
                canManageUsers: true,
                canManageSettings: true,
                canViewAuditTrail: true,
                canApproveExpenses: true,
                canDeleteRecords: true,
                canExportData: true,
                canAccessAllData: true,
                description: 'Full system access'
            },
            manager: {
                canManageUsers: false,
                canManageSettings: false,
                canViewAuditTrail: false,
                canApproveExpenses: true,
                canDeleteRecords: false,
                canExportData: false,
                canAccessAllData: true,
                description: 'Can approve expenses and view reports'
            },
            accountant: {
                canManageUsers: false,
                canManageSettings: false,
                canViewAuditTrail: false,
                canApproveExpenses: false,
                canDeleteRecords: false,
                canExportData: false,
                canAccessAllData: true,
                description: 'Can manage invoices and expenses'
            },
            auditor: {
                canManageUsers: false,
                canManageSettings: false,
                canViewAuditTrail: true,
                canApproveExpenses: false,
                canDeleteRecords: false,
                canExportData: true,
                canAccessAllData: true,
                readOnlyAccess: true,
                description: 'Read-only access for auditing'
            },
            employee: {
                canManageUsers: false,
                canManageSettings: false,
                canViewAuditTrail: false,
                canApproveExpenses: false,
                canDeleteRecords: false,
                canExportData: false,
                canAccessAllData: false,
                ownDataOnly: true,
                description: 'Limited access to own data only'
            }
        };

        return limitations[this.currentUser.role] || limitations.employee;
    }

    // Show MFA input field
    showMFAInput() {
        const mfaGroup = document.getElementById('mfaGroup');
        if (mfaGroup) {
            mfaGroup.style.display = 'block';
            document.getElementById('mfaToken').focus();
        }
    }

    // Hide MFA input field
    hideMFAInput() {
        const mfaGroup = document.getElementById('mfaGroup');
        if (mfaGroup) {
            mfaGroup.style.display = 'none';
            document.getElementById('mfaToken').value = '';
        }
    }

    // Validate form data
    validateLoginForm(formData) {
        const errors = [];

        if (!formData.credential || formData.credential.trim().length < 3) {
            errors.push('Username or email must be at least 3 characters long');
        }

        if (!formData.password || formData.password.length < 1) {
            errors.push('Password is required');
        }

        return errors;
    }

    validateRegisterForm(formData) {
        const errors = [];

        if (!formData.firstName || formData.firstName.trim().length < 2) {
            errors.push('First name must be at least 2 characters long');
        }

        if (!formData.lastName || formData.lastName.trim().length < 2) {
            errors.push('Last name must be at least 2 characters long');
        }

        if (!formData.username || formData.username.trim().length < 3) {
            errors.push('Username must be at least 3 characters long');
        }

        if (!formData.email || !this.isValidEmail(formData.email)) {
            errors.push('Please enter a valid email address');
        }

        if (!formData.password || formData.password.length < 1) {
            errors.push('Password is required');
        }

        if (formData.password !== formData.confirmPassword) {
            errors.push('Passwords do not match');
        }

        return errors;
    }

    // Email validation
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Create global auth manager instance
const authManager = new AuthManager();

// Initialize login form
function initializeLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(loginForm);
        const loginData = {
            credential: formData.get('credential'),
            password: formData.get('password'),
            mfaToken: formData.get('mfaToken')
        };

        // Validate form
        const errors = authManager.validateLoginForm(loginData);
        if (errors.length > 0) {
            authManager.showAlert(errors.join('. '), 'error');
            return;
        }

        try {
            await authManager.login(loginData.credential, loginData.password, loginData.mfaToken);
        } catch (error) {
            console.error('Login failed:', error);
        }
    });
}

// Initialize register form
function initializeRegisterForm() {
    const registerForm = document.getElementById('registerForm');
    if (!registerForm) return;

    registerForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(registerForm);
        const registerData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            username: formData.get('username'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            role: formData.get('role'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword')
        };

        // Validate form
        const errors = authManager.validateRegisterForm(registerData);
        if (errors.length > 0) {
            authManager.showAlert(errors.join('. '), 'error');
            return;
        }

        try {
            await authManager.register(registerData);
        } catch (error) {
            console.error('Registration failed:', error);
        }
    });
}

// Check authentication on protected pages
function requireAuth() {
    console.log('🛡️ Checking page authentication...');

    if (authManager.isAuthenticated()) {
        console.log('✅ Access granted');
        return true;
    }

    console.log('❌ Access denied - redirecting to login');
    // Add a small delay to prevent immediate redirect loops
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 100);
    return false;
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { authManager, requireAuth };
}
