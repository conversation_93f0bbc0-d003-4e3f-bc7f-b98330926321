// Customer Management JavaScript
class CustomerManager {
    constructor() {
        this.customers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.currentCustomer = null;
    }

    // Initialize customers page
    async initialize() {
        try {
            this.showLoading();
            await this.loadCustomers();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize customers page:', error);
            this.hideLoading();
            this.showAlert('Failed to load customers data', 'error');
        }
    }

    // Load customers
    async loadCustomers() {
        try {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: searchTerm,
                status: statusFilter
            });

            const response = await apiClient.request(`/customers?${params}`);
            this.customers = response.data.customers;
            this.updatePagination(response.data.pagination);
            this.renderCustomersTable();
        } catch (error) {
            console.error('Failed to load customers:', error);
            this.showAlert('Failed to load customers', 'error');
        }
    }

    // Render customers table
    renderCustomersTable() {
        const tbody = document.getElementById('customersTableBody');
        tbody.innerHTML = '';

        if (this.customers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">No customers found</td>
                </tr>
            `;
            return;
        }

        this.customers.forEach(customer => {
            const outstanding = parseFloat(customer.total_billed) - parseFloat(customer.total_paid);
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <a href="#" onclick="viewCustomer(${customer.id})" class="customer-link">
                        ${customer.customer_code}
                    </a>
                </td>
                <td>${customer.name}</td>
                <td>${customer.email || '-'}</td>
                <td>${customer.phone || '-'}</td>
                <td>${formatUtils.currency(customer.total_billed)}</td>
                <td>${formatUtils.currency(customer.total_paid)}</td>
                <td class="${outstanding > 0 ? 'text-warning' : ''}">
                    ${formatUtils.currency(outstanding)}
                </td>
                <td>
                    <span class="status-badge status-${customer.is_active ? 'active' : 'inactive'}">
                        ${customer.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="editCustomer(${customer.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="createInvoiceForCustomer(${customer.id})" title="Create Invoice">
                            <i class="fas fa-file-invoice"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="viewCustomer(${customer.id})" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCustomer(${customer.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Update pagination
    updatePagination(pagination) {
        this.currentPage = pagination.page;
        this.totalPages = pagination.pages;

        // Update pagination info
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        document.getElementById('paginationInfo').textContent = 
            `Showing ${start}-${end} of ${pagination.total} customers`;

        // Update pagination controls
        document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
        document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;

        // Update page numbers
        this.renderPageNumbers();
    }

    // Render page numbers
    renderPageNumbers() {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }

    // Go to specific page
    async goToPage(page) {
        this.currentPage = page;
        await this.loadCustomers();
    }

    // Change page (next/previous)
    async changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1 && newPage <= this.totalPages) {
            await this.goToPage(newPage);
        }
    }

    // Show create customer modal
    showCreateCustomerModal() {
        this.currentCustomer = null;
        document.getElementById('modalTitle').textContent = 'Add New Customer';
        document.getElementById('customerForm').reset();
        document.getElementById('statusGroup').style.display = 'none';
        document.getElementById('customerModal').style.display = 'flex';
    }

    // Show edit customer modal
    async showEditCustomerModal(customerId) {
        try {
            this.showLoading();
            const response = await apiClient.request(`/customers/${customerId}`);
            this.currentCustomer = response.data.customer;
            
            document.getElementById('modalTitle').textContent = 'Edit Customer';
            this.populateCustomerForm(this.currentCustomer);
            document.getElementById('statusGroup').style.display = 'block';
            document.getElementById('customerModal').style.display = 'flex';
            
            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to load customer details', 'error');
        }
    }

    // Populate customer form
    populateCustomerForm(customer) {
        document.getElementById('customerName').value = customer.name || '';
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('taxNumber').value = customer.tax_number || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerCity').value = customer.city || '';
        document.getElementById('customerCountry').value = customer.country || '';
        document.getElementById('creditLimit').value = customer.credit_limit || 0;
        document.getElementById('paymentTerms').value = customer.payment_terms || 30;
        document.getElementById('customerStatus').value = customer.is_active ? '1' : '0';
    }

    // Close customer modal
    closeCustomerModal() {
        document.getElementById('customerModal').style.display = 'none';
        this.currentCustomer = null;
    }

    // Save customer
    async saveCustomer() {
        try {
            this.showLoading();
            
            // Collect form data
            const formData = new FormData(document.getElementById('customerForm'));
            const customerData = Object.fromEntries(formData.entries());
            
            // Convert numeric fields
            customerData.credit_limit = parseFloat(customerData.credit_limit) || 0;
            customerData.payment_terms = parseInt(customerData.payment_terms) || 30;
            customerData.is_active = customerData.is_active === '1';
            
            // Validate required fields
            if (!customerData.name) {
                throw new Error('Customer name is required');
            }
            
            // Save customer
            const endpoint = this.currentCustomer ? `/customers/${this.currentCustomer.id}` : '/customers';
            const method = this.currentCustomer ? 'PUT' : 'POST';
            
            const response = await apiClient.request(endpoint, {
                method,
                body: JSON.stringify(customerData)
            });
            
            this.hideLoading();
            this.showAlert('Customer saved successfully!', 'success');
            this.closeCustomerModal();
            await this.loadCustomers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save customer', 'error');
        }
    }

    // View customer details
    async viewCustomer(customerId) {
        try {
            this.showLoading();
            const response = await apiClient.request(`/customers/${customerId}`);
            const { customer, recentInvoices } = response.data;
            
            this.renderCustomerDetails(customer, recentInvoices);
            document.getElementById('customerDetailsModal').style.display = 'flex';
            
            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to load customer details', 'error');
        }
    }

    // Render customer details
    renderCustomerDetails(customer, recentInvoices) {
        const outstanding = parseFloat(customer.total_billed) - parseFloat(customer.total_paid);
        
        const content = document.querySelector('.customer-details-content');
        content.innerHTML = `
            <div class="customer-info-grid">
                <div class="info-section">
                    <h4>Basic Information</h4>
                    <div class="info-row">
                        <span class="label">Customer Code:</span>
                        <span class="value">${customer.customer_code}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Name:</span>
                        <span class="value">${customer.name}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Email:</span>
                        <span class="value">${customer.email || '-'}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Phone:</span>
                        <span class="value">${customer.phone || '-'}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Status:</span>
                        <span class="value">
                            <span class="status-badge status-${customer.is_active ? 'active' : 'inactive'}">
                                ${customer.is_active ? 'Active' : 'Inactive'}
                            </span>
                        </span>
                    </div>
                </div>

                <div class="info-section">
                    <h4>Financial Summary</h4>
                    <div class="info-row">
                        <span class="label">Total Billed:</span>
                        <span class="value">${formatUtils.currency(customer.total_billed)}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Total Paid:</span>
                        <span class="value">${formatUtils.currency(customer.total_paid)}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Outstanding:</span>
                        <span class="value ${outstanding > 0 ? 'text-warning' : ''}">
                            ${formatUtils.currency(outstanding)}
                        </span>
                    </div>
                    <div class="info-row">
                        <span class="label">Credit Limit:</span>
                        <span class="value">${formatUtils.currency(customer.credit_limit)}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Payment Terms:</span>
                        <span class="value">${customer.payment_terms} days</span>
                    </div>
                </div>
            </div>

            <div class="recent-invoices-section">
                <h4>Recent Invoices</h4>
                <div class="invoices-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${recentInvoices.length > 0 ? 
                                recentInvoices.map(invoice => `
                                    <tr>
                                        <td>${invoice.invoice_number}</td>
                                        <td>${formatUtils.date(invoice.invoice_date)}</td>
                                        <td>${formatUtils.date(invoice.due_date)}</td>
                                        <td>${formatUtils.currency(invoice.total_amount)}</td>
                                        <td>
                                            <span class="status-badge status-${invoice.status}">
                                                ${invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('') :
                                '<tr><td colspan="5" class="text-center">No invoices found</td></tr>'
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        document.getElementById('detailsModalTitle').textContent = `${customer.name} - Details`;
        this.currentCustomer = customer;
    }

    // Close customer details modal
    closeCustomerDetailsModal() {
        document.getElementById('customerDetailsModal').style.display = 'none';
        this.currentCustomer = null;
    }

    // Edit customer from details modal
    editCustomerFromDetails() {
        this.closeCustomerDetailsModal();
        this.showEditCustomerModal(this.currentCustomer.id);
    }

    // Delete customer
    async deleteCustomer(customerId) {
        if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/customers/${customerId}`, { method: 'DELETE' });
            
            this.hideLoading();
            this.showAlert('Customer deleted successfully!', 'success');
            await this.loadCustomers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to delete customer', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        document.getElementById('searchInput').addEventListener('input', 
            debounce(() => this.loadCustomers(), 300));
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        // Implementation depends on your alert system
        console.log(`${type.toUpperCase()}: ${message}`);
        // You can implement a toast notification system here
    }
}

// Global customer manager instance
const customerManager = new CustomerManager();

// Global functions for HTML onclick handlers
function initializeCustomersPage() {
    customerManager.initialize();
}

function showCreateCustomerModal() {
    customerManager.showCreateCustomerModal();
}

function closeCustomerModal() {
    customerManager.closeCustomerModal();
}

function closeCustomerDetailsModal() {
    customerManager.closeCustomerDetailsModal();
}

function editCustomerFromDetails() {
    customerManager.editCustomerFromDetails();
}

function saveCustomer() {
    customerManager.saveCustomer();
}

function filterCustomers() {
    customerManager.currentPage = 1;
    customerManager.loadCustomers();
}

function changePage(direction) {
    customerManager.changePage(direction);
}

function editCustomer(id) {
    customerManager.showEditCustomerModal(id);
}

function viewCustomer(id) {
    customerManager.viewCustomer(id);
}

function deleteCustomer(id) {
    customerManager.deleteCustomer(id);
}

function createInvoiceForCustomer(customerId) {
    // Redirect to invoice creation with pre-selected customer
    window.location.href = `invoices.html?action=create&customer=${customerId}`;
}

function exportCustomers() {
    // Implementation for exporting customers
    console.log('Export customers');
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
