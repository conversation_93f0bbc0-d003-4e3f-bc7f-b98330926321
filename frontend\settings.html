<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>

            <div class="menu-item active admin-only">
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>System Settings</h1>
                <p>Configure system preferences and company information</p>
            </div>
        </header>

        <!-- Settings Tabs -->
        <div class="settings-tabs">
            <button class="tab-button active" onclick="showTab('company')">
                <i class="fas fa-building"></i>
                Company Information
            </button>
            <button class="tab-button" onclick="showTab('accounting')">
                <i class="fas fa-calculator"></i>
                Accounting Settings
            </button>
            <button class="tab-button" onclick="showTab('security')">
                <i class="fas fa-shield-alt"></i>
                Security Settings
            </button>
            <button class="tab-button" onclick="showTab('notifications')">
                <i class="fas fa-bell"></i>
                Notifications
            </button>
            <button class="tab-button" onclick="showTab('backup')">
                <i class="fas fa-database"></i>
                Backup & Restore
            </button>
        </div>

        <!-- Company Information Tab -->
        <div class="tab-content active" id="company-tab">
            <div class="settings-card">
                <h3>Company Information</h3>
                <form id="companyForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="companyName">Company Name *</label>
                            <input type="text" id="companyName" name="name" required placeholder="Enter company name">
                        </div>
                        <div class="form-group">
                            <label for="companyEmail">Email</label>
                            <input type="email" id="companyEmail" name="email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="companyPhone">Phone</label>
                            <input type="tel" id="companyPhone" name="phone" placeholder="+****************">
                        </div>
                        <div class="form-group">
                            <label for="companyWebsite">Website</label>
                            <input type="url" id="companyWebsite" name="website" placeholder="https://www.company.com">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="companyAddress">Address</label>
                        <textarea id="companyAddress" name="address" rows="3" placeholder="Company address"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="companyCity">City</label>
                            <input type="text" id="companyCity" name="city" placeholder="City">
                        </div>
                        <div class="form-group">
                            <label for="companyState">State/Province</label>
                            <input type="text" id="companyState" name="state" placeholder="State">
                        </div>
                        <div class="form-group">
                            <label for="companyCountry">Country</label>
                            <input type="text" id="companyCountry" name="country" placeholder="Country">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="taxNumber">Tax Number</label>
                            <input type="text" id="taxNumber" name="tax_number" placeholder="Tax ID or VAT number">
                        </div>
                        <div class="form-group">
                            <label for="registrationNumber">Registration Number</label>
                            <input type="text" id="registrationNumber" name="registration_number" placeholder="Business registration number">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveCompanySettings()">
                            <i class="fas fa-save"></i>
                            Save Company Information
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Accounting Settings Tab -->
        <div class="tab-content" id="accounting-tab">
            <div class="settings-card">
                <h3>Accounting Preferences</h3>
                <form id="accountingForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="baseCurrency">Base Currency *</label>
                            <select id="baseCurrency" name="base_currency" required>
                                <option value="USD">USD - US Dollar</option>
                                <option value="EUR">EUR - Euro</option>
                                <option value="GBP">GBP - British Pound</option>
                                <option value="CAD">CAD - Canadian Dollar</option>
                                <option value="AUD">AUD - Australian Dollar</option>
                                <option value="JPY">JPY - Japanese Yen</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="fiscalYearStart">Fiscal Year Start</label>
                            <select id="fiscalYearStart" name="fiscal_year_start">
                                <option value="01-01">January 1</option>
                                <option value="04-01">April 1</option>
                                <option value="07-01">July 1</option>
                                <option value="10-01">October 1</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="defaultTaxRate">Default Tax Rate (%)</label>
                            <input type="number" id="defaultTaxRate" name="default_tax_rate" step="0.01" min="0" max="100" value="0">
                        </div>
                        <div class="form-group">
                            <label for="invoicePrefix">Invoice Number Prefix</label>
                            <input type="text" id="invoicePrefix" name="invoice_prefix" placeholder="INV" maxlength="10">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="paymentTerms">Default Payment Terms (Days)</label>
                            <input type="number" id="paymentTerms" name="default_payment_terms" min="1" value="30">
                        </div>
                        <div class="form-group">
                            <label for="dateFormat">Date Format</label>
                            <select id="dateFormat" name="date_format">
                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveAccountingSettings()">
                            <i class="fas fa-save"></i>
                            Save Accounting Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Security Settings Tab -->
        <div class="tab-content" id="security-tab">
            <div class="settings-card">
                <h3>Security Configuration</h3>
                <form id="securityForm">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="requireMFA" name="require_mfa">
                            Require Multi-Factor Authentication for all users
                        </label>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enforcePasswordPolicy" name="enforce_password_policy">
                            Enforce strong password policy
                        </label>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="sessionTimeout">Session Timeout (Minutes)</label>
                            <input type="number" id="sessionTimeout" name="session_timeout" min="5" max="1440" value="60">
                        </div>
                        <div class="form-group">
                            <label for="maxLoginAttempts">Max Login Attempts</label>
                            <input type="number" id="maxLoginAttempts" name="max_login_attempts" min="3" max="10" value="5">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="enableAuditLog" name="enable_audit_log" checked>
                            Enable comprehensive audit logging
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveSecuritySettings()">
                            <i class="fas fa-save"></i>
                            Save Security Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notifications Tab -->
        <div class="tab-content" id="notifications-tab">
            <div class="settings-card">
                <h3>Notification Preferences</h3>
                <form id="notificationsForm">
                    <div class="notification-section">
                        <h4>Email Notifications</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="emailInvoiceReminders" name="email_invoice_reminders" checked>
                                Send invoice payment reminders
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="emailExpenseApprovals" name="email_expense_approvals" checked>
                                Send expense approval notifications
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="emailSystemAlerts" name="email_system_alerts" checked>
                                Send system alerts and updates
                            </label>
                        </div>
                    </div>

                    <div class="notification-section">
                        <h4>Dashboard Notifications</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="dashboardOverdue" name="dashboard_overdue" checked>
                                Show overdue invoice alerts
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="dashboardExpenses" name="dashboard_expenses" checked>
                                Show pending expense approvals
                            </label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveNotificationSettings()">
                            <i class="fas fa-save"></i>
                            Save Notification Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Backup & Restore Tab -->
        <div class="tab-content" id="backup-tab">
            <div class="settings-card">
                <h3>Backup & Restore</h3>
                
                <div class="backup-section">
                    <h4>Database Backup</h4>
                    <p>Create a backup of your accounting data including all transactions, customers, and settings.</p>
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="createBackup()">
                            <i class="fas fa-download"></i>
                            Create Backup
                        </button>
                    </div>
                </div>

                <div class="backup-section">
                    <h4>Restore from Backup</h4>
                    <p>Restore your data from a previously created backup file.</p>
                    <div class="form-group">
                        <label for="backupFile">Select Backup File</label>
                        <input type="file" id="backupFile" accept=".sql,.zip">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                            <i class="fas fa-upload"></i>
                            Restore Backup
                        </button>
                    </div>
                </div>

                <div class="backup-section">
                    <h4>Automatic Backups</h4>
                    <form id="autoBackupForm">
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableAutoBackup" name="enable_auto_backup">
                                Enable automatic daily backups
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="backupRetention">Backup Retention (Days)</label>
                            <input type="number" id="backupRetention" name="backup_retention" min="1" max="365" value="30">
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="saveBackupSettings()">
                                <i class="fas fa-save"></i>
                                Save Backup Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/settings.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Settings page loading...');

            // Check authentication with delay to ensure auth manager is ready
            setTimeout(() => {
                if (!requireAuth()) {
                    console.log('❌ Authentication failed, stopping page initialization');
                    return;
                }

                // Check admin permissions
                const currentUser = authManager.getCurrentUser();
                if (!currentUser || currentUser.role !== 'admin') {
                    console.log('❌ Admin access required, redirecting to dashboard');
                    window.location.href = 'dashboard.html';
                    return;
                }

                console.log('✅ Authentication and admin check successful, initializing settings page...');
                // Initialize settings page
                initializeSettingsPage();
            }, 50);
        });
    </script>
</body>
</html>
