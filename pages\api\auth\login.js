// Working login API endpoint - replaces the broken one
export default function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('=== WORKING LOGIN ENDPOINT ===');
    console.log('Request body:', req.body);

    const { credential, password } = req.body || {};

    if (!credential || !password) {
      console.log('Missing credentials');
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Simple credential check
    const validCredentials = {
      'admin': 'admin',
      'manager': 'manager',
      'accountant': 'accountant'
    };

    if (validCredentials[credential] !== password) {
      console.log('Invalid credentials');
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    console.log('Credentials valid');

    // Create simple token
    const simpleToken = `token_${credential}_${Date.now()}`;

    // User data based on credential
    const userData = {
      admin: {
        id: 1,
        username: 'admin',
        first_name: 'Admin',
        last_name: 'User',
        email: '<EMAIL>',
        role: 'admin'
      },
      manager: {
        id: 2,
        username: 'manager',
        first_name: 'Manager',
        last_name: 'User',
        email: '<EMAIL>',
        role: 'manager'
      },
      accountant: {
        id: 3,
        username: 'accountant',
        first_name: 'Accountant',
        last_name: 'User',
        email: '<EMAIL>',
        role: 'accountant'
      }
    };

    const user = userData[credential];

    const response = {
      success: true,
      message: 'Login successful',
      accessToken: simpleToken,
      user: user,
      expiresIn: '24h'
    };

    console.log('Sending response:', response);
    res.status(200).json(response);

  } catch (error) {
    console.error('=== LOGIN ERROR ===');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    
    res.status(500).json({ 
      error: 'Internal server error',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
