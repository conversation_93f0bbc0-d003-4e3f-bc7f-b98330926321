<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
        }
        .debug-info {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 1rem 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1><i class="fas fa-bug"></i> Authentication Debug Tool</h1>
        <p>This page helps debug authentication issues in the accounting software.</p>

        <div class="debug-section">
            <h3>Authentication Status</h3>
            <div id="authStatus">
                <span class="status-indicator status-warning"></span>
                <span>Checking...</span>
            </div>
            <div class="debug-info" id="authInfo">Loading authentication information...</div>
        </div>

        <div class="debug-section">
            <h3>LocalStorage Data</h3>
            <div class="debug-info" id="storageInfo">Loading storage information...</div>
        </div>

        <div class="debug-section">
            <h3>Authentication Manager State</h3>
            <div class="debug-info" id="managerInfo">Loading manager information...</div>
        </div>

        <div class="debug-section">
            <h3>Actions</h3>
            <button class="btn btn-primary" onclick="refreshDebugInfo()">
                <i class="fas fa-refresh"></i> Refresh Info
            </button>
            <button class="btn btn-secondary" onclick="clearAuthData()">
                <i class="fas fa-trash"></i> Clear Auth Data
            </button>
            <button class="btn btn-success" onclick="testLogin()">
                <i class="fas fa-sign-in-alt"></i> Test Login
            </button>
            <button class="btn btn-warning" onclick="goToLogin()">
                <i class="fas fa-arrow-left"></i> Go to Login
            </button>
            <button class="btn btn-info" onclick="goToDashboard()">
                <i class="fas fa-tachometer-alt"></i> Go to Dashboard
            </button>
        </div>

        <div class="debug-section">
            <h3>Console Logs</h3>
            <div class="debug-info" id="consoleLogs">Console logs will appear here...</div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // Capture console logs
        const originalLog = console.log;
        const logs = [];
        
        console.log = function(...args) {
            logs.push(new Date().toLocaleTimeString() + ': ' + args.join(' '));
            if (logs.length > 20) logs.shift(); // Keep only last 20 logs
            updateConsoleLogs();
            originalLog.apply(console, arguments);
        };

        function updateConsoleLogs() {
            document.getElementById('consoleLogs').textContent = logs.join('\n');
        }

        function refreshDebugInfo() {
            console.log('🔄 Refreshing debug information...');
            
            // Check authentication status
            const isAuth = authManager.isAuthenticated();
            const statusEl = document.getElementById('authStatus');
            
            if (isAuth) {
                statusEl.innerHTML = '<span class="status-indicator status-success"></span><span>Authenticated ✅</span>';
            } else {
                statusEl.innerHTML = '<span class="status-indicator status-error"></span><span>Not Authenticated ❌</span>';
            }

            // Show localStorage data
            const accessToken = localStorage.getItem('accessToken');
            const currentUser = localStorage.getItem('currentUser');
            
            const storageInfo = `Access Token: ${accessToken ? 'Present ✅' : 'Missing ❌'}
Token Value: ${accessToken || 'null'}

Current User: ${currentUser ? 'Present ✅' : 'Missing ❌'}
User Data: ${currentUser || 'null'}`;
            
            document.getElementById('storageInfo').textContent = storageInfo;

            // Show auth manager state
            const managerInfo = `Auth Manager Token: ${authManager.accessToken || 'null'}
Auth Manager User: ${JSON.stringify(authManager.currentUser, null, 2) || 'null'}
Is Authenticated: ${authManager.isAuthenticated()}`;
            
            document.getElementById('managerInfo').textContent = managerInfo;

            // Show detailed auth info
            const authInfo = `Authentication Check Details:
- Has Token: ${!!authManager.accessToken}
- Has User: ${!!authManager.currentUser}
- User ID: ${authManager.currentUser?.id || 'N/A'}
- User Role: ${authManager.currentUser?.role || 'N/A'}
- User Name: ${authManager.currentUser?.firstName} ${authManager.currentUser?.lastName || ''}

RequireAuth Result: ${requireAuth()}`;
            
            document.getElementById('authInfo').textContent = authInfo;
        }

        function clearAuthData() {
            console.log('🗑️ Clearing authentication data...');
            localStorage.removeItem('accessToken');
            localStorage.removeItem('currentUser');
            authManager.accessToken = null;
            authManager.currentUser = null;
            refreshDebugInfo();
        }

        async function testLogin() {
            console.log('🧪 Testing login with admin/admin...');
            try {
                await authManager.login('admin', 'admin');
                console.log('✅ Login test successful');
                refreshDebugInfo();
            } catch (error) {
                console.log('❌ Login test failed:', error.message);
                refreshDebugInfo();
            }
        }

        function goToLogin() {
            console.log('🔄 Redirecting to login page...');
            window.location.href = 'index.html';
        }

        function goToDashboard() {
            console.log('🔄 Redirecting to dashboard...');
            window.location.href = 'dashboard.html';
        }

        // Initialize debug info when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Debug page loaded');
            setTimeout(refreshDebugInfo, 100);
        });
    </script>
</body>
</html>
