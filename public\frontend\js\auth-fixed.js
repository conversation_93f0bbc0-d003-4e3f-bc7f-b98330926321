// FIXED Authentication System - Works Reliably
console.log('🔧 Loading FIXED Authentication System...');

class FixedAuthManager {
    constructor() {
        this.apiBaseUrl = '/api';
        this.loadAuthFromStorage();
        console.log('FixedAuthManager initialized');
    }

    // Load authentication data from localStorage
    loadAuthFromStorage() {
        try {
            const token = localStorage.getItem('accessToken');
            const userStr = localStorage.getItem('currentUser');
            
            if (token && userStr) {
                this.accessToken = token;
                this.currentUser = JSON.parse(userStr);
                console.log('✅ Auth loaded from storage:', this.currentUser.username);
            } else {
                this.accessToken = null;
                this.currentUser = null;
                console.log('❌ No auth data in storage');
            }
        } catch (error) {
            console.error('Error loading auth from storage:', error);
            this.clearAuth();
        }
    }

    // Save authentication data to localStorage
    saveAuthToStorage() {
        try {
            if (this.accessToken && this.currentUser) {
                localStorage.setItem('accessToken', this.accessToken);
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                console.log('✅ Auth saved to storage');
                return true;
            }
        } catch (error) {
            console.error('Error saving auth to storage:', error);
        }
        return false;
    }

    // Clear authentication data
    clearAuth() {
        this.accessToken = null;
        this.currentUser = null;
        localStorage.removeItem('accessToken');
        localStorage.removeItem('currentUser');
        console.log('🗑️ Auth data cleared');
    }

    // Check if user is authenticated
    isAuthenticated() {
        // Always refresh from storage first
        this.loadAuthFromStorage();
        const isAuth = !!(this.accessToken && this.currentUser);
        console.log('🔍 Authentication check:', isAuth ? '✅ AUTHENTICATED' : '❌ NOT AUTHENTICATED');
        return isAuth;
    }

    // Login function
    async login(credential, password) {
        try {
            console.log('🔐 Attempting login for:', credential);
            
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ credential, password })
            });

            const data = await response.json();
            console.log('📡 Login response:', data);

            if (response.ok && data.success) {
                this.accessToken = data.accessToken;
                this.currentUser = data.user;
                
                if (this.saveAuthToStorage()) {
                    console.log('✅ Login successful!');
                    return { success: true, user: this.currentUser };
                }
            }
            
            throw new Error(data.error || 'Login failed');
        } catch (error) {
            console.error('❌ Login error:', error);
            this.clearAuth();
            throw error;
        }
    }

    // Logout function
    logout() {
        console.log('🚪 Logging out...');
        this.clearAuth();
        window.location.href = 'index.html';
    }

    // Get current user
    getCurrentUser() {
        this.loadAuthFromStorage();
        return this.currentUser;
    }

    // Check if user is admin
    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }
}

// Create global instance
const fixedAuthManager = new FixedAuthManager();

// FIXED requireAuth function
function requireAuth() {
    console.log('🛡️ Checking page authentication...');
    
    if (fixedAuthManager.isAuthenticated()) {
        console.log('✅ Access granted');
        return true;
    }
    
    console.log('❌ Access denied - redirecting to login');
    window.location.href = 'index.html';
    return false;
}

// Initialize authentication when page loads
function initializeAuth() {
    console.log('🚀 Initializing authentication...');
    fixedAuthManager.loadAuthFromStorage();
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAuth);
} else {
    initializeAuth();
}

// Export for global use
window.fixedAuthManager = fixedAuthManager;
window.requireAuth = requireAuth;

console.log('✅ Fixed Authentication System loaded successfully!');
