<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Enterprise Accounting</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info span {
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .welcome-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .welcome-section h2 {
            color: #333;
            margin-bottom: 10px;
        }

        .welcome-section p {
            color: #666;
            font-size: 16px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .metric-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .metric-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-card .change {
            font-size: 14px;
            font-weight: 500;
        }

        .change.positive { color: #28a745; }
        .change.negative { color: #dc3545; }

        .navigation {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navigation h3 {
            margin-bottom: 20px;
            color: #333;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .nav-item {
            display: block;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
            text-align: center;
        }

        .nav-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .nav-item .icon {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }

        .status {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }

        .status h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .status-indicator.online { background-color: #28a745; }
        .status-indicator.working { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <h1>🏢 Enterprise Accounting</h1>
            </div>
            <div class="user-info">
                <span id="userInfo">Loading...</span>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="welcome-section">
            <h2 id="welcomeMessage">Welcome to Enterprise Accounting</h2>
            <p>Your comprehensive business management dashboard</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <h3>Total Revenue</h3>
                <div class="value">$125,000</div>
                <div class="change positive">+12.5% from last month</div>
            </div>
            <div class="metric-card">
                <h3>Total Expenses</h3>
                <div class="value">$85,000</div>
                <div class="change positive">+8.2% from last month</div>
            </div>
            <div class="metric-card">
                <h3>Net Profit</h3>
                <div class="value">$40,000</div>
                <div class="change positive">+15.3% from last month</div>
            </div>
            <div class="metric-card">
                <h3>Pending Invoices</h3>
                <div class="value">12</div>
                <div class="change negative">3 overdue</div>
            </div>
        </div>

        <div class="navigation">
            <h3>📊 Quick Access</h3>
            <div class="nav-grid">
                <a href="frontend/invoices.html" class="nav-item">
                    <span class="icon">📄</span>
                    <div>Invoices</div>
                </a>
                <a href="frontend/customers.html" class="nav-item">
                    <span class="icon">👥</span>
                    <div>Customers</div>
                </a>
                <a href="frontend/suppliers.html" class="nav-item">
                    <span class="icon">🏭</span>
                    <div>Suppliers</div>
                </a>
                <a href="frontend/expenses.html" class="nav-item">
                    <span class="icon">💰</span>
                    <div>Expenses</div>
                </a>
                <a href="frontend/reports.html" class="nav-item">
                    <span class="icon">📈</span>
                    <div>Reports</div>
                </a>
                <a href="frontend/settings.html" class="nav-item" id="settingsLink" style="display: none;">
                    <span class="icon">⚙️</span>
                    <div>Settings</div>
                </a>
            </div>
        </div>

        <div class="status">
            <h3>🔧 System Status</h3>
            <div class="status-item">
                <div style="display: flex; align-items: center;">
                    <div class="status-indicator online"></div>
                    <span>Authentication System</span>
                </div>
                <span>✅ Working</span>
            </div>
            <div class="status-item">
                <div style="display: flex; align-items: center;">
                    <div class="status-indicator online"></div>
                    <span>Dashboard API</span>
                </div>
                <span>✅ Working</span>
            </div>
            <div class="status-item">
                <div style="display: flex; align-items: center;">
                    <div class="status-indicator working"></div>
                    <span>Database Connection</span>
                </div>
                <span>⚠️ Demo Mode</span>
            </div>
        </div>
    </div>

    <script src="frontend/js/auth-fixed.js"></script>
    <script>
        // Check authentication
        console.log('🛡️ Checking dashboard authentication...');
        if (!requireAuth()) {
            // Will redirect to login if not authenticated
            console.log('❌ Not authenticated, should redirect');
        } else {
            console.log('✅ Authentication passed, loading dashboard');
            
            // Load user info
            const user = fixedAuthManager.getCurrentUser();
            if (user) {
                document.getElementById('userInfo').textContent = `${user.first_name} ${user.last_name} (${user.role})`;
                document.getElementById('welcomeMessage').textContent = `Welcome back, ${user.first_name}!`;
                
                // Show admin-only features
                if (user.role === 'admin') {
                    document.getElementById('settingsLink').style.display = 'block';
                }
            }
        }

        function logout() {
            console.log('🚪 Logging out from dashboard...');
            fixedAuthManager.logout();
        }

        console.log('✅ Dashboard loaded successfully');
    </script>
</body>
</html>
