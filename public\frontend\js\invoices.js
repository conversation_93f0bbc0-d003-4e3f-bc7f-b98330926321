// Invoice Management JavaScript
class InvoiceManager {
    constructor() {
        this.invoices = [];
        this.customers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.currentInvoice = null;
        this.invoiceItems = [];
    }

    // Initialize invoices page
    async initialize() {
        try {
            this.showLoading();
            await this.loadCustomers();
            await this.loadInvoices();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize invoices page:', error);
            this.hideLoading();
            this.showAlert('Failed to load invoices data', 'error');
        }
    }

    // Load customers for dropdown
    async loadCustomers() {
        try {
            const response = await apiClient.request('/customers?limit=1000');
            this.customers = response.data.customers;
            this.populateCustomerDropdown();
        } catch (error) {
            console.error('Failed to load customers:', error);
        }
    }

    // Populate customer dropdown
    populateCustomerDropdown() {
        const customerSelect = document.getElementById('customerId');
        customerSelect.innerHTML = '<option value="">Select Customer</option>';
        
        this.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = `${customer.name} (${customer.customer_code})`;
            customerSelect.appendChild(option);
        });
    }

    // Load invoices
    async loadInvoices() {
        try {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const periodFilter = document.getElementById('periodFilter').value;

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: searchTerm,
                status: statusFilter,
                period: periodFilter
            });

            const response = await apiClient.request(`/invoices?${params}`);
            this.invoices = response.data.invoices;
            this.updatePagination(response.data.pagination);
            this.renderInvoicesTable();
        } catch (error) {
            console.error('Failed to load invoices:', error);
            this.showAlert('Failed to load invoices', 'error');
        }
    }

    // Render invoices table
    renderInvoicesTable() {
        const tbody = document.getElementById('invoicesTableBody');
        tbody.innerHTML = '';

        if (this.invoices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No invoices found</td>
                </tr>
            `;
            return;
        }

        this.invoices.forEach(invoice => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <a href="#" onclick="viewInvoice(${invoice.id})" class="invoice-link">
                        ${invoice.invoice_number}
                    </a>
                </td>
                <td>${this.getCustomerName(invoice.customer_id)}</td>
                <td>${formatUtils.date(invoice.invoice_date)}</td>
                <td>${formatUtils.date(invoice.due_date)}</td>
                <td>${formatUtils.currency(invoice.total_amount)}</td>
                <td>${formatUtils.currency(invoice.paid_amount)}</td>
                <td>
                    <span class="status-badge status-${invoice.status}">
                        ${invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="editInvoice(${invoice.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="printInvoice(${invoice.id})" title="Print">
                            <i class="fas fa-print"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="recordPayment(${invoice.id})" title="Record Payment">
                            <i class="fas fa-dollar-sign"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteInvoice(${invoice.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Get customer name by ID
    getCustomerName(customerId) {
        const customer = this.customers.find(c => c.id === customerId);
        return customer ? customer.name : 'Unknown Customer';
    }

    // Update pagination
    updatePagination(pagination) {
        this.currentPage = pagination.page;
        this.totalPages = pagination.pages;

        // Update pagination info
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        document.getElementById('paginationInfo').textContent = 
            `Showing ${start}-${end} of ${pagination.total} invoices`;

        // Update pagination controls
        document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
        document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;

        // Update page numbers
        this.renderPageNumbers();
    }

    // Render page numbers
    renderPageNumbers() {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }

    // Go to specific page
    async goToPage(page) {
        this.currentPage = page;
        await this.loadInvoices();
    }

    // Change page (next/previous)
    async changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1 && newPage <= this.totalPages) {
            await this.goToPage(newPage);
        }
    }

    // Show create invoice modal
    showCreateInvoiceModal() {
        this.currentInvoice = null;
        this.invoiceItems = [];
        document.getElementById('modalTitle').textContent = 'Create New Invoice';
        document.getElementById('invoiceForm').reset();
        
        // Set default dates
        const today = new Date().toISOString().split('T')[0];
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30);
        
        document.getElementById('invoiceDate').value = today;
        document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
        
        this.clearInvoiceItems();
        this.addInvoiceItem(); // Add first item
        this.updateInvoiceSummary();
        
        document.getElementById('invoiceModal').style.display = 'flex';
    }

    // Close invoice modal
    closeInvoiceModal() {
        document.getElementById('invoiceModal').style.display = 'none';
        this.currentInvoice = null;
        this.invoiceItems = [];
    }

    // Add invoice item
    addInvoiceItem() {
        const itemsContainer = document.getElementById('invoiceItems');
        const itemIndex = this.invoiceItems.length;
        
        const itemDiv = document.createElement('div');
        itemDiv.className = 'invoice-item';
        itemDiv.innerHTML = `
            <div class="item-row">
                <div class="form-group">
                    <input type="text" placeholder="Description" class="item-description" required>
                </div>
                <div class="form-group">
                    <input type="number" placeholder="Qty" class="item-quantity" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <input type="number" placeholder="Unit Price" class="item-price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <input type="text" class="item-total" readonly placeholder="Total">
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-danger btn-sm" onclick="invoiceManager.removeInvoiceItem(${itemIndex})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        itemsContainer.appendChild(itemDiv);
        
        // Add event listeners for calculation
        const quantityInput = itemDiv.querySelector('.item-quantity');
        const priceInput = itemDiv.querySelector('.item-price');
        
        quantityInput.addEventListener('input', () => this.calculateItemTotal(itemDiv));
        priceInput.addEventListener('input', () => this.calculateItemTotal(itemDiv));
        
        this.invoiceItems.push({
            description: '',
            quantity: 0,
            unit_price: 0,
            total_price: 0
        });
    }

    // Remove invoice item
    removeInvoiceItem(index) {
        const itemsContainer = document.getElementById('invoiceItems');
        const items = itemsContainer.querySelectorAll('.invoice-item');
        
        if (items.length > 1) {
            items[index].remove();
            this.invoiceItems.splice(index, 1);
            this.updateInvoiceSummary();
        }
    }

    // Calculate item total
    calculateItemTotal(itemDiv) {
        const quantity = parseFloat(itemDiv.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemDiv.querySelector('.item-price').value) || 0;
        const total = quantity * price;
        
        itemDiv.querySelector('.item-total').value = formatUtils.currency(total);
        this.updateInvoiceSummary();
    }

    // Clear invoice items
    clearInvoiceItems() {
        document.getElementById('invoiceItems').innerHTML = '';
        this.invoiceItems = [];
    }

    // Update invoice summary
    updateInvoiceSummary() {
        const items = document.querySelectorAll('.invoice-item');
        let subtotal = 0;
        
        items.forEach(item => {
            const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(item.querySelector('.item-price').value) || 0;
            subtotal += quantity * price;
        });
        
        const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;
        
        const discountedAmount = subtotal - discountAmount;
        const taxAmount = (discountedAmount * taxRate) / 100;
        const total = discountedAmount + taxAmount;
        
        document.getElementById('subtotalAmount').textContent = formatUtils.currency(subtotal);
        document.getElementById('discountAmountDisplay').textContent = formatUtils.currency(discountAmount);
        document.getElementById('taxAmountDisplay').textContent = formatUtils.currency(taxAmount);
        document.getElementById('totalAmount').textContent = formatUtils.currency(total);
    }

    // Save invoice
    async saveInvoice() {
        try {
            this.showLoading();
            
            // Collect form data
            const formData = new FormData(document.getElementById('invoiceForm'));
            const invoiceData = Object.fromEntries(formData.entries());
            
            // Collect items data
            const items = [];
            const itemElements = document.querySelectorAll('.invoice-item');
            
            itemElements.forEach(item => {
                const description = item.querySelector('.item-description').value;
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const unit_price = parseFloat(item.querySelector('.item-price').value) || 0;
                
                if (description && quantity > 0 && unit_price > 0) {
                    items.push({
                        description,
                        quantity,
                        unit_price
                    });
                }
            });
            
            if (items.length === 0) {
                throw new Error('Please add at least one invoice item');
            }
            
            invoiceData.items = items;
            
            // Save invoice
            const endpoint = this.currentInvoice ? `/invoices/${this.currentInvoice.id}` : '/invoices';
            const method = this.currentInvoice ? 'PUT' : 'POST';
            
            const response = await apiClient.request(endpoint, {
                method,
                body: JSON.stringify(invoiceData)
            });
            
            this.hideLoading();
            this.showAlert('Invoice saved successfully!', 'success');
            this.closeInvoiceModal();
            await this.loadInvoices();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save invoice', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Search and filter inputs
        document.getElementById('searchInput').addEventListener('input', 
            debounce(() => this.loadInvoices(), 300));
        
        // Invoice summary calculation
        document.getElementById('discountAmount').addEventListener('input', () => this.updateInvoiceSummary());
        document.getElementById('taxRate').addEventListener('input', () => this.updateInvoiceSummary());
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        // Implementation depends on your alert system
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Global invoice manager instance
const invoiceManager = new InvoiceManager();

// Global functions for HTML onclick handlers
function initializeInvoicesPage() {
    invoiceManager.initialize();
}

function showCreateInvoiceModal() {
    invoiceManager.showCreateInvoiceModal();
}

function closeInvoiceModal() {
    invoiceManager.closeInvoiceModal();
}

function addInvoiceItem() {
    invoiceManager.addInvoiceItem();
}

function saveInvoice() {
    invoiceManager.saveInvoice();
}

function filterInvoices() {
    invoiceManager.currentPage = 1;
    invoiceManager.loadInvoices();
}

function changePage(direction) {
    invoiceManager.changePage(direction);
}

function editInvoice(id) {
    // Implementation for editing invoice
    console.log('Edit invoice:', id);
}

function viewInvoice(id) {
    // Implementation for viewing invoice
    console.log('View invoice:', id);
}

function printInvoice(id) {
    // Implementation for printing invoice
    console.log('Print invoice:', id);
}

function recordPayment(id) {
    // Implementation for recording payment
    console.log('Record payment for invoice:', id);
}

function deleteInvoice(id) {
    if (confirm('Are you sure you want to delete this invoice?')) {
        // Implementation for deleting invoice
        console.log('Delete invoice:', id);
    }
}

function exportInvoices() {
    // Implementation for exporting invoices
    console.log('Export invoices');
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
