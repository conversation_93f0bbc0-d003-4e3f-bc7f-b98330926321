/* Dashboard Specific Styles */

/* Sidebar Styles */
.sidebar-header {
  padding: var(--spacing-6) var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header .logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-weight: 600;
  color: var(--gray-900);
}

.sidebar-header .logo i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
}

.sidebar-toggle {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  color: var(--gray-600);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.sidebar-menu {
  flex: 1;
  padding: var(--spacing-4) 0;
  overflow-y: auto;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin-bottom: var(--spacing-1);
}

.menu-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-700);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

.menu-link:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
  text-decoration: none;
}

.menu-item.active .menu-link {
  background-color: var(--primary-color);
  color: var(--white);
}

.menu-link i {
  width: 20px;
  text-align: center;
  font-size: var(--font-size-base);
}

.badge {
  background-color: var(--error-color);
  color: var(--white);
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.submenu-arrow {
  margin-left: auto;
  transition: transform var(--transition-fast);
}

.menu-item.expanded .submenu-arrow {
  transform: rotate(180deg);
}

.submenu {
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: var(--gray-50);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.submenu.expanded {
  max-height: 200px;
}

.submenu li {
  padding: 0;
}

.submenu a {
  display: block;
  padding: var(--spacing-2) var(--spacing-4) var(--spacing-2) calc(var(--spacing-4) + 32px);
  color: var(--gray-600);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.submenu a:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
  text-decoration: none;
}

.sidebar-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}

.user-role {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
  text-transform: capitalize;
}

.logout-btn {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  color: var(--gray-600);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background-color: var(--error-color);
  color: var(--white);
}

/* Top Header */
.top-header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  color: var(--gray-600);
  border-radius: var(--radius-md);
}

.mobile-menu-toggle:hover {
  background-color: var(--gray-100);
}

.page-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.header-btn {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  color: var(--gray-600);
  border-radius: var(--radius-md);
  position: relative;
  transition: all var(--transition-fast);
}

.header-btn:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--error-color);
  color: var(--white);
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.user-menu {
  position: relative;
}

.user-menu-btn {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.user-menu-btn:hover {
  background-color: var(--gray-100);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 180px;
  padding: var(--spacing-2);
  display: none;
  z-index: 1000;
}

.user-dropdown.show {
  display: block;
}

.user-dropdown a {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--gray-700);
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.user-dropdown a:hover {
  background-color: var(--gray-50);
  color: var(--primary-color);
  text-decoration: none;
}

.user-dropdown hr {
  border: none;
  border-top: 1px solid var(--gray-200);
  margin: var(--spacing-2) 0;
}

/* Dashboard Content */
.dashboard-content {
  padding: var(--spacing-6);
  flex: 1;
}

/* Access Info Banner */
.access-info-banner {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.access-info-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
}

.access-info-text {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: #92400e;
  font-weight: 500;
}

.access-info-text i {
  font-size: var(--font-size-lg);
}

.access-info-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #92400e;
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.access-info-close:hover {
  background-color: rgba(146, 64, 14, 0.1);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.metric-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: all var(--transition-fast);
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
}

.metric-icon.success {
  background-color: var(--success-color);
}

.metric-icon.warning {
  background-color: var(--warning-color);
}

.metric-icon.info {
  background-color: var(--info-color);
}

.metric-icon.primary {
  background-color: var(--primary-color);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin: 0 0 var(--spacing-1) 0;
}

.metric-label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-2) 0;
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: 500;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.metric-change.positive {
  background-color: #d1fae5;
  color: #065f46;
}

.metric-change.negative {
  background-color: #fee2e2;
  color: #991b1b;
}

.metric-change.neutral {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }

  .top-header {
    padding: var(--spacing-4);
  }

  .dashboard-content {
    padding: var(--spacing-4);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .metric-card {
    padding: var(--spacing-4);
  }

  .sidebar.collapsed .user-details,
  .sidebar.collapsed .menu-link span,
  .sidebar.collapsed .badge {
    display: none;
  }

  .sidebar.collapsed .sidebar-header .logo span {
    display: none;
  }
}
