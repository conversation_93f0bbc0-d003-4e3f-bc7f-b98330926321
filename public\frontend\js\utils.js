// Utility functions for the accounting application

// API utility class
class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    // Get authorization header
    getAuthHeader() {
        const token = localStorage.getItem('accessToken');
        return token ? { 'Authorization': `Bearer ${token}` } : {};
    }

    // Make API request
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                ...this.defaultHeaders,
                ...this.getAuthHeader(),
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            
            // Handle authentication errors
            if (error.message.includes('401') || error.message.includes('token')) {
                localStorage.removeItem('accessToken');
                localStorage.removeItem('currentUser');
                window.location.href = 'index.html';
            }
            
            throw error;
        }
    }

    // GET request
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    // POST request
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(endpoint) {
        return this.request(endpoint, { method: 'DELETE' });
    }
}

// Global API client instance
const apiClient = new ApiClient();

// Formatting utilities
const formatUtils = {
    // Format currency
    currency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    // Format number
    number(value, decimals = 2) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(value);
    },

    // Format percentage
    percentage(value, decimals = 1) {
        return new Intl.NumberFormat('en-US', {
            style: 'percent',
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(value / 100);
    },

    // Format date
    date(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
    },

    // Format date and time
    datetime(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
    },

    // Format time ago
    timeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - new Date(date)) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
        return `${Math.floor(diffInSeconds / 31536000)} years ago`;
    }
};

// Validation utilities
const validationUtils = {
    // Email validation
    email(email) {
        const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return regex.test(email);
    },

    // Phone validation
    phone(phone) {
        const regex = /^[\+]?[1-9][\d]{0,15}$/;
        return regex.test(phone.replace(/\s/g, ''));
    },

    // Required field validation
    required(value) {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    },

    // Minimum length validation
    minLength(value, min) {
        return value && value.toString().length >= min;
    },

    // Maximum length validation
    maxLength(value, max) {
        return value && value.toString().length <= max;
    },

    // Number validation
    number(value) {
        return !isNaN(value) && !isNaN(parseFloat(value));
    },

    // Positive number validation
    positiveNumber(value) {
        return this.number(value) && parseFloat(value) > 0;
    },

    // Date validation
    date(value) {
        return !isNaN(Date.parse(value));
    },

    // Future date validation
    futureDate(value) {
        return this.date(value) && new Date(value) > new Date();
    }
};

// DOM utilities
const domUtils = {
    // Get element by ID
    get(id) {
        return document.getElementById(id);
    },

    // Get elements by class name
    getByClass(className) {
        return document.getElementsByClassName(className);
    },

    // Get elements by query selector
    query(selector) {
        return document.querySelector(selector);
    },

    // Get elements by query selector all
    queryAll(selector) {
        return document.querySelectorAll(selector);
    },

    // Create element
    create(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else if (key === 'innerHTML') {
                element.innerHTML = attributes[key];
            } else {
                element.setAttribute(key, attributes[key]);
            }
        });

        if (content) {
            element.textContent = content;
        }

        return element;
    },

    // Show element
    show(element) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.style.display = '';
        }
    },

    // Hide element
    hide(element) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.style.display = 'none';
        }
    },

    // Toggle element visibility
    toggle(element) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.style.display = element.style.display === 'none' ? '' : 'none';
        }
    },

    // Add class
    addClass(element, className) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.classList.add(className);
        }
    },

    // Remove class
    removeClass(element, className) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.classList.remove(className);
        }
    },

    // Toggle class
    toggleClass(element, className) {
        if (typeof element === 'string') {
            element = this.get(element);
        }
        if (element) {
            element.classList.toggle(className);
        }
    }
};

// Storage utilities
const storageUtils = {
    // Set item in localStorage
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    },

    // Get item from localStorage
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return defaultValue;
        }
    },

    // Remove item from localStorage
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error removing from localStorage:', error);
        }
    },

    // Clear all localStorage
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Error clearing localStorage:', error);
        }
    }
};

// Notification utilities
const notificationUtils = {
    // Show success notification
    success(message, duration = 5000) {
        this.show(message, 'success', duration);
    },

    // Show error notification
    error(message, duration = 8000) {
        this.show(message, 'error', duration);
    },

    // Show warning notification
    warning(message, duration = 6000) {
        this.show(message, 'warning', duration);
    },

    // Show info notification
    info(message, duration = 5000) {
        this.show(message, 'info', duration);
    },

    // Show notification
    show(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = domUtils.create('div', {
            className: `notification notification-${type}`,
            innerHTML: `
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `
        });

        // Add to page
        let container = domUtils.get('notification-container');
        if (!container) {
            container = domUtils.create('div', { id: 'notification-container', className: 'notification-container' });
            document.body.appendChild(container);
        }

        container.appendChild(notification);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }
    }
};

// Export utilities for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        apiClient,
        formatUtils,
        validationUtils,
        domUtils,
        storageUtils,
        notificationUtils
    };
}
