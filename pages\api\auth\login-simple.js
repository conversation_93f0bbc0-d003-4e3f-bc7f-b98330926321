// Simplified login API that works without database
import jwt from 'jsonwebtoken';

// Demo users (replace with database later)
const DEMO_USERS = [
  {
    id: 1,
    username: 'admin',
    password: 'admin', // In production, this would be hashed
    first_name: 'Admin',
    last_name: 'User',
    email: '<EMAIL>',
    role: 'admin',
    is_active: true,
    permissions: ['*'] // Admin has all permissions
  },
  {
    id: 2,
    username: 'manager',
    password: 'manager',
    first_name: 'Manager',
    last_name: 'User',
    email: '<EMAIL>',
    role: 'manager',
    is_active: true,
    permissions: ['invoices.*', 'customers.*', 'reports.*', 'expenses.*']
  },
  {
    id: 3,
    username: 'accountant',
    password: 'accountant',
    first_name: 'Accountant',
    last_name: 'User',
    email: '<EMAIL>',
    role: 'accountant',
    is_active: true,
    permissions: ['invoices.*', 'customers.read', 'expenses.*']
  }
];

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Login attempt received:', req.body);
    const { credential, password } = req.body;

    if (!credential || !password) {
      console.log('Missing credentials');
      return res.status(400).json({ error: 'Username and password are required' });
    }

    // Find user
    const user = DEMO_USERS.find(u => 
      u.username === credential || u.email === credential
    );

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password (in production, use bcrypt)
    if (user.password !== password) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check if user is active
    if (!user.is_active) {
      return res.status(401).json({ error: 'Account is disabled' });
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key-for-development';
    const token = jwt.sign(
      {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      },
      jwtSecret,
      { expiresIn: '24h' }
    );

    // Return success response (matching frontend expectations)
    res.status(200).json({
      success: true,
      message: 'Login successful',
      accessToken: token,
      user: {
        id: user.id,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      },
      expiresIn: '24h'
    });

  } catch (error) {
    console.error('Login error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      error: 'Internal server error',
      details: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
