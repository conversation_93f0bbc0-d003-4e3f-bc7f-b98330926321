// Settings Management JavaScript
class SettingsManager {
    constructor() {
        this.currentTab = 'company';
        this.settings = {};
    }

    // Initialize settings page
    async initialize() {
        try {
            this.showLoading();
            await this.loadSettings();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize settings page:', error);
            this.hideLoading();
            this.showAlert('Failed to load settings', 'error');
        }
    }

    // Load all settings
    async loadSettings() {
        try {
            const response = await apiClient.request('/settings');
            this.settings = response.data;
            this.populateAllForms();
        } catch (error) {
            console.error('Failed to load settings:', error);
            // Use default values if settings don't exist
            this.settings = this.getDefaultSettings();
            this.populateAllForms();
        }
    }

    // Get default settings
    getDefaultSettings() {
        return {
            company: {
                name: 'Your Company Name',
                email: '',
                phone: '',
                website: '',
                address: '',
                city: '',
                state: '',
                country: '',
                tax_number: '',
                registration_number: ''
            },
            accounting: {
                base_currency: 'USD',
                fiscal_year_start: '01-01',
                default_tax_rate: 0,
                invoice_prefix: 'INV',
                default_payment_terms: 30,
                date_format: 'MM/DD/YYYY'
            },
            security: {
                require_mfa: false,
                enforce_password_policy: true,
                session_timeout: 60,
                max_login_attempts: 5,
                enable_audit_log: true
            },
            notifications: {
                email_invoice_reminders: true,
                email_expense_approvals: true,
                email_system_alerts: true,
                dashboard_overdue: true,
                dashboard_expenses: true
            },
            backup: {
                enable_auto_backup: false,
                backup_retention: 30
            }
        };
    }

    // Populate all forms with current settings
    populateAllForms() {
        this.populateCompanyForm();
        this.populateAccountingForm();
        this.populateSecurityForm();
        this.populateNotificationsForm();
        this.populateBackupForm();
    }

    // Populate company form
    populateCompanyForm() {
        const company = this.settings.company || {};
        document.getElementById('companyName').value = company.name || '';
        document.getElementById('companyEmail').value = company.email || '';
        document.getElementById('companyPhone').value = company.phone || '';
        document.getElementById('companyWebsite').value = company.website || '';
        document.getElementById('companyAddress').value = company.address || '';
        document.getElementById('companyCity').value = company.city || '';
        document.getElementById('companyState').value = company.state || '';
        document.getElementById('companyCountry').value = company.country || '';
        document.getElementById('taxNumber').value = company.tax_number || '';
        document.getElementById('registrationNumber').value = company.registration_number || '';
    }

    // Populate accounting form
    populateAccountingForm() {
        const accounting = this.settings.accounting || {};
        document.getElementById('baseCurrency').value = accounting.base_currency || 'USD';
        document.getElementById('fiscalYearStart').value = accounting.fiscal_year_start || '01-01';
        document.getElementById('defaultTaxRate').value = accounting.default_tax_rate || 0;
        document.getElementById('invoicePrefix').value = accounting.invoice_prefix || 'INV';
        document.getElementById('paymentTerms').value = accounting.default_payment_terms || 30;
        document.getElementById('dateFormat').value = accounting.date_format || 'MM/DD/YYYY';
    }

    // Populate security form
    populateSecurityForm() {
        const security = this.settings.security || {};
        document.getElementById('requireMFA').checked = security.require_mfa || false;
        document.getElementById('enforcePasswordPolicy').checked = security.enforce_password_policy !== false;
        document.getElementById('sessionTimeout').value = security.session_timeout || 60;
        document.getElementById('maxLoginAttempts').value = security.max_login_attempts || 5;
        document.getElementById('enableAuditLog').checked = security.enable_audit_log !== false;
    }

    // Populate notifications form
    populateNotificationsForm() {
        const notifications = this.settings.notifications || {};
        document.getElementById('emailInvoiceReminders').checked = notifications.email_invoice_reminders !== false;
        document.getElementById('emailExpenseApprovals').checked = notifications.email_expense_approvals !== false;
        document.getElementById('emailSystemAlerts').checked = notifications.email_system_alerts !== false;
        document.getElementById('dashboardOverdue').checked = notifications.dashboard_overdue !== false;
        document.getElementById('dashboardExpenses').checked = notifications.dashboard_expenses !== false;
    }

    // Populate backup form
    populateBackupForm() {
        const backup = this.settings.backup || {};
        document.getElementById('enableAutoBackup').checked = backup.enable_auto_backup || false;
        document.getElementById('backupRetention').value = backup.backup_retention || 30;
    }

    // Show tab
    showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab
        document.getElementById(`${tabName}-tab`).classList.add('active');
        event.target.classList.add('active');
        this.currentTab = tabName;
    }

    // Save company settings
    async saveCompanySettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('companyForm'));
            const companyData = Object.fromEntries(formData.entries());
            
            if (!companyData.name) {
                throw new Error('Company name is required');
            }
            
            await apiClient.request('/settings/company', {
                method: 'PUT',
                body: JSON.stringify(companyData)
            });
            
            this.settings.company = companyData;
            this.hideLoading();
            this.showAlert('Company settings saved successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save company settings', 'error');
        }
    }

    // Save accounting settings
    async saveAccountingSettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('accountingForm'));
            const accountingData = Object.fromEntries(formData.entries());
            
            // Convert numeric fields
            accountingData.default_tax_rate = parseFloat(accountingData.default_tax_rate) || 0;
            accountingData.default_payment_terms = parseInt(accountingData.default_payment_terms) || 30;
            
            await apiClient.request('/settings/accounting', {
                method: 'PUT',
                body: JSON.stringify(accountingData)
            });
            
            this.settings.accounting = accountingData;
            this.hideLoading();
            this.showAlert('Accounting settings saved successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save accounting settings', 'error');
        }
    }

    // Save security settings
    async saveSecuritySettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('securityForm'));
            const securityData = Object.fromEntries(formData.entries());
            
            // Convert boolean fields
            securityData.require_mfa = securityData.require_mfa === 'on';
            securityData.enforce_password_policy = securityData.enforce_password_policy === 'on';
            securityData.enable_audit_log = securityData.enable_audit_log === 'on';
            
            // Convert numeric fields
            securityData.session_timeout = parseInt(securityData.session_timeout) || 60;
            securityData.max_login_attempts = parseInt(securityData.max_login_attempts) || 5;
            
            await apiClient.request('/settings/security', {
                method: 'PUT',
                body: JSON.stringify(securityData)
            });
            
            this.settings.security = securityData;
            this.hideLoading();
            this.showAlert('Security settings saved successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save security settings', 'error');
        }
    }

    // Save notification settings
    async saveNotificationSettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('notificationsForm'));
            const notificationData = Object.fromEntries(formData.entries());
            
            // Convert boolean fields
            Object.keys(notificationData).forEach(key => {
                notificationData[key] = notificationData[key] === 'on';
            });
            
            await apiClient.request('/settings/notifications', {
                method: 'PUT',
                body: JSON.stringify(notificationData)
            });
            
            this.settings.notifications = notificationData;
            this.hideLoading();
            this.showAlert('Notification settings saved successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save notification settings', 'error');
        }
    }

    // Save backup settings
    async saveBackupSettings() {
        try {
            this.showLoading();
            
            const formData = new FormData(document.getElementById('autoBackupForm'));
            const backupData = Object.fromEntries(formData.entries());
            
            // Convert boolean and numeric fields
            backupData.enable_auto_backup = backupData.enable_auto_backup === 'on';
            backupData.backup_retention = parseInt(backupData.backup_retention) || 30;
            
            await apiClient.request('/settings/backup', {
                method: 'PUT',
                body: JSON.stringify(backupData)
            });
            
            this.settings.backup = backupData;
            this.hideLoading();
            this.showAlert('Backup settings saved successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save backup settings', 'error');
        }
    }

    // Create backup
    async createBackup() {
        try {
            this.showLoading();
            
            const response = await apiClient.request('/settings/backup/create', {
                method: 'POST'
            });
            
            // Create download link
            const blob = new Blob([response.data], { type: 'application/sql' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup_${new Date().toISOString().split('T')[0]}.sql`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.hideLoading();
            this.showAlert('Backup created and downloaded successfully!', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to create backup', 'error');
        }
    }

    // Restore backup
    async restoreBackup() {
        const fileInput = document.getElementById('backupFile');
        if (!fileInput.files[0]) {
            this.showAlert('Please select a backup file', 'warning');
            return;
        }

        if (!confirm('Are you sure you want to restore from backup? This will overwrite all current data.')) {
            return;
        }

        try {
            this.showLoading();
            
            const formData = new FormData();
            formData.append('backup', fileInput.files[0]);
            
            await apiClient.request('/settings/backup/restore', {
                method: 'POST',
                body: formData
            });
            
            this.hideLoading();
            this.showAlert('Backup restored successfully! Please refresh the page.', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to restore backup', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Tab switching is handled by onclick in HTML
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // You can implement a toast notification system here
    }
}

// Global settings manager instance
const settingsManager = new SettingsManager();

// Global functions for HTML onclick handlers
function initializeSettingsPage() {
    settingsManager.initialize();
}

function showTab(tabName) {
    settingsManager.showTab(tabName);
}

function saveCompanySettings() {
    settingsManager.saveCompanySettings();
}

function saveAccountingSettings() {
    settingsManager.saveAccountingSettings();
}

function saveSecuritySettings() {
    settingsManager.saveSecuritySettings();
}

function saveNotificationSettings() {
    settingsManager.saveNotificationSettings();
}

function saveBackupSettings() {
    settingsManager.saveBackupSettings();
}

function createBackup() {
    settingsManager.createBackup();
}

function restoreBackup() {
    settingsManager.restoreBackup();
}
