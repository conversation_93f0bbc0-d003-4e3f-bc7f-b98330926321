// User Management JavaScript
class UserManager {
    constructor() {
        this.users = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.currentUser = null;
    }

    // Initialize users page
    async initialize() {
        try {
            this.showLoading();
            await this.loadUsers();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize users page:', error);
            this.hideLoading();
            this.showAlert('Failed to load users data', 'error');
        }
    }

    // Load users
    async loadUsers() {
        try {
            const searchTerm = document.getElementById('searchInput').value;
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: searchTerm,
                role: roleFilter,
                status: statusFilter
            });

            const response = await apiClient.request(`/users?${params}`);
            this.users = response.data.users;
            this.updatePagination(response.data.pagination);
            this.renderUsersTable();
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showAlert('Failed to load users', 'error');
        }
    }

    // Render users table
    renderUsersTable() {
        const tbody = document.getElementById('usersTableBody');
        tbody.innerHTML = '';

        if (this.users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">No users found</td>
                </tr>
            `;
            return;
        }

        const currentUserId = authManager.getCurrentUser().id;

        this.users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.first_name} ${user.last_name}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td>
                    <span class="status-badge status-${user.role}">
                        ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </span>
                </td>
                <td>
                    <span class="status-badge status-${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>${user.last_login ? formatUtils.datetime(user.last_login) : 'Never'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="editUser(${user.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${user.id !== currentUserId ? `
                            <button class="btn btn-sm btn-warning" onclick="toggleUserStatus(${user.id}, ${!user.is_active})" title="${user.is_active ? 'Deactivate' : 'Activate'}">
                                <i class="fas fa-${user.is_active ? 'ban' : 'check'}"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : `
                            <span class="text-muted">Current User</span>
                        `}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Update pagination
    updatePagination(pagination) {
        this.currentPage = pagination.page;
        this.totalPages = pagination.pages;

        // Update pagination info
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        document.getElementById('paginationInfo').textContent = 
            `Showing ${start}-${end} of ${pagination.total} users`;

        // Update pagination controls
        document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
        document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;

        // Update page numbers
        this.renderPageNumbers();
    }

    // Render page numbers
    renderPageNumbers() {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }

    // Go to specific page
    async goToPage(page) {
        this.currentPage = page;
        await this.loadUsers();
    }

    // Change page (next/previous)
    async changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1 && newPage <= this.totalPages) {
            await this.goToPage(newPage);
        }
    }

    // Show create user modal
    showCreateUserModal() {
        this.currentUser = null;
        document.getElementById('modalTitle').textContent = 'Add New User';
        document.getElementById('userForm').reset();
        document.getElementById('statusGroup').style.display = 'none';
        document.getElementById('passwordSection').style.display = 'block';
        document.getElementById('password').required = true;
        document.getElementById('confirmPassword').required = true;
        document.getElementById('userModal').style.display = 'flex';
    }

    // Show edit user modal
    async showEditUserModal(userId) {
        try {
            this.showLoading();
            const response = await apiClient.request(`/users/${userId}`);
            this.currentUser = response.data.user;
            
            document.getElementById('modalTitle').textContent = 'Edit User';
            this.populateUserForm(this.currentUser);
            document.getElementById('statusGroup').style.display = 'block';
            document.getElementById('passwordSection').style.display = 'none';
            document.getElementById('password').required = false;
            document.getElementById('confirmPassword').required = false;
            document.getElementById('userModal').style.display = 'flex';
            
            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to load user details', 'error');
        }
    }

    // Populate user form
    populateUserForm(user) {
        document.getElementById('firstName').value = user.first_name || '';
        document.getElementById('lastName').value = user.last_name || '';
        document.getElementById('username').value = user.username || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('role').value = user.role || '';
        document.getElementById('userStatus').value = user.is_active ? '1' : '0';
    }

    // Close user modal
    closeUserModal() {
        document.getElementById('userModal').style.display = 'none';
        this.currentUser = null;
    }

    // Save user
    async saveUser() {
        try {
            this.showLoading();
            
            // Collect form data
            const formData = new FormData(document.getElementById('userForm'));
            const userData = Object.fromEntries(formData.entries());
            
            // Validate required fields
            if (!userData.firstName || !userData.lastName || !userData.username || !userData.email || !userData.role) {
                throw new Error('Please fill in all required fields');
            }

            // Validate passwords for new users
            if (!this.currentUser) {
                if (!userData.password || !userData.confirmPassword) {
                    throw new Error('Password is required for new users');
                }
                if (userData.password !== userData.confirmPassword) {
                    throw new Error('Passwords do not match');
                }
            }

            // Convert boolean fields
            userData.is_active = userData.is_active === '1';
            
            // Save user
            const endpoint = this.currentUser ? `/users/${this.currentUser.id}` : '/users';
            const method = this.currentUser ? 'PUT' : 'POST';
            
            const response = await apiClient.request(endpoint, {
                method,
                body: JSON.stringify(userData)
            });
            
            this.hideLoading();
            this.showAlert('User saved successfully!', 'success');
            this.closeUserModal();
            await this.loadUsers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save user', 'error');
        }
    }

    // Toggle user status
    async toggleUserStatus(userId, newStatus) {
        const action = newStatus ? 'activate' : 'deactivate';
        if (!confirm(`Are you sure you want to ${action} this user?`)) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify({ is_active: newStatus })
            });
            
            this.hideLoading();
            this.showAlert(`User ${action}d successfully!`, 'success');
            await this.loadUsers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(`Failed to ${action} user`, 'error');
        }
    }

    // Delete user
    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/users/${userId}`, { method: 'DELETE' });
            
            this.hideLoading();
            this.showAlert('User deleted successfully!', 'success');
            await this.loadUsers();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to delete user', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        document.getElementById('searchInput').addEventListener('input', 
            debounce(() => this.loadUsers(), 300));
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        // Implementation depends on your alert system
        console.log(`${type.toUpperCase()}: ${message}`);
        // You can implement a toast notification system here
    }
}

// Global user manager instance
const userManager = new UserManager();

// Global functions for HTML onclick handlers
function initializeUsersPage() {
    userManager.initialize();
}

function showCreateUserModal() {
    userManager.showCreateUserModal();
}

function closeUserModal() {
    userManager.closeUserModal();
}

function saveUser() {
    userManager.saveUser();
}

function filterUsers() {
    userManager.currentPage = 1;
    userManager.loadUsers();
}

function changePage(direction) {
    userManager.changePage(direction);
}

function editUser(id) {
    userManager.showEditUserModal(id);
}

function toggleUserStatus(id, status) {
    userManager.toggleUserStatus(id, status);
}

function deleteUser(id) {
    userManager.deleteUser(id);
}

function exportUsers() {
    // Implementation for exporting users
    console.log('Export users');
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
