<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item active admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>User Management</h1>
                <p>Manage system users and their permissions</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-plus"></i>
                    Add User
                </button>
            </div>
        </header>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search users..." onkeyup="filterUsers()">
            </div>
            <div class="filter-controls">
                <select id="roleFilter" onchange="filterUsers()">
                    <option value="all">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="manager">Manager</option>
                    <option value="accountant">Accountant</option>
                    <option value="auditor">Auditor</option>
                    <option value="employee">Employee</option>
                </select>
                <select id="statusFilter" onchange="filterUsers()">
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <button class="btn btn-secondary" onclick="exportUsers()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Users Table -->
        <div class="table-container">
            <table class="data-table" id="usersTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- Users will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 0 of 0 users</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="changePage(-1)" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span class="page-numbers" id="pageNumbers"></span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="changePage(1)" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Create/Edit User Modal -->
    <div class="modal" id="userModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New User</h3>
                <button class="modal-close" onclick="closeUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required placeholder="Enter first name">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required placeholder="Enter last name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" required placeholder="Enter username">
                        </div>
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">Phone</label>
                            <input type="tel" id="phone" name="phone" placeholder="+****************">
                        </div>
                        <div class="form-group">
                            <label for="role">Role *</label>
                            <select id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="employee">Employee</option>
                                <option value="accountant">Accountant</option>
                                <option value="manager">Manager</option>
                                <option value="auditor">Auditor</option>
                                <option value="admin">Administrator</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row" id="passwordSection">
                        <div class="form-group">
                            <label for="password">Password *</label>
                            <input type="password" id="password" name="password" placeholder="Enter password">
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password *</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm password">
                        </div>
                    </div>

                    <div class="form-group" id="statusGroup" style="display: none;">
                        <label for="userStatus">Status</label>
                        <select id="userStatus" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeUserModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">
                    <i class="fas fa-save"></i>
                    Save User
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth-fixed.js"></script>
    <script src="js/users.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!requireAuth()) return;

            // Check admin permissions
            const currentUser = authManager.getCurrentUser();
            if (currentUser.role !== 'admin') {
                window.location.href = 'dashboard.html';
                return;
            }

            // Initialize users page
            initializeUsersPage();
        });
    </script>
</body>
</html>

        <div class="sidebar-menu">
            <ul class="menu-list">
                <li class="menu-item">
                    <a href="dashboard.html" class="menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="invoices.html" class="menu-link">
                        <i class="fas fa-file-invoice"></i>
                        <span>Invoices</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="expenses.html" class="menu-link">
                        <i class="fas fa-receipt"></i>
                        <span>Expenses</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="payroll.html" class="menu-link">
                        <i class="fas fa-users"></i>
                        <span>Payroll</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="reports.html" class="menu-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a>
                </li>
                <li class="menu-item active admin-only">
                    <a href="users.html" class="menu-link">
                        <i class="fas fa-users-cog"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="menu-item admin-only">
                    <a href="settings.html" class="menu-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="userName">Loading...</span>
                    <span class="user-role" id="userRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="authManager.logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">User Management</h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="header-btn" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-menu-btn" id="userMenuBtn">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="settings.html"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="#" onclick="authManager.logout()"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- User Management Content -->
        <div class="dashboard-content">
            <!-- Admin Only Notice -->
            <div class="access-info-banner" style="background: linear-gradient(135deg, #d1fae5, #a7f3d0); border-color: #10b981;">
                <div class="access-info-content">
                    <div class="access-info-text" style="color: #065f46;">
                        <i class="fas fa-crown"></i>
                        <span>Administrator Access: You have full user management privileges</span>
                    </div>
                </div>
            </div>

            <!-- Action Bar -->
            <div class="action-bar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <div class="search-filters">
                    <input type="text" id="userSearch" placeholder="Search users..." style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem; margin-right: 1rem;">
                    <select id="roleFilter" style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 0.375rem;">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="accountant">Accountant</option>
                        <option value="auditor">Auditor</option>
                        <option value="employee">Employee</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="showCreateUserModal()">
                    <i class="fas fa-user-plus"></i>
                    Create New User
                </button>
            </div>

            <!-- Users Table -->
            <div class="table-container" style="background: white; border-radius: 0.5rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1); overflow: hidden;">
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Create User Modal -->
    <div class="modal-overlay" id="createUserModal" style="display: none;">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Create New User</h3>
                <button class="modal-close" onclick="hideCreateUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createUserForm" class="modal-body">
                <div class="form-row">
                    <div class="form-group">
                        <label for="newFirstName">First Name</label>
                        <input type="text" id="newFirstName" name="firstName" required>
                    </div>
                    <div class="form-group">
                        <label for="newLastName">Last Name</label>
                        <input type="text" id="newLastName" name="lastName" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="newUsername">Username</label>
                    <input type="text" id="newUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="newEmail">Email</label>
                    <input type="email" id="newEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="newPhone">Phone (Optional)</label>
                    <input type="tel" id="newPhone" name="phone">
                </div>
                <div class="form-group">
                    <label for="newRole">Role</label>
                    <select id="newRole" name="role" required>
                        <option value="employee">Employee</option>
                        <option value="accountant">Accountant</option>
                        <option value="manager">Manager</option>
                        <option value="auditor">Auditor</option>
                        <option value="admin">Administrator</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password</label>
                    <input type="password" id="newPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="confirmNewPassword">Confirm Password</label>
                    <input type="password" id="confirmNewPassword" name="confirmPassword" required>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideCreateUserModal()">Cancel</button>
                <button type="submit" form="createUserForm" class="btn btn-primary">Create User</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading users...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth-fixed.js"></script>
    <script>
        // Initialize user management page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication and admin access
            if (!requireAuth()) return;
            
            const user = authManager.getCurrentUser();
            if (!user || user.role !== 'admin') {
                alert('Access Denied: Only administrators can access user management.');
                window.location.href = 'dashboard.html';
                return;
            }

            // Load user info
            document.getElementById('userName').textContent = `${user.firstName} ${user.lastName}`;
            document.getElementById('userRole').textContent = user.role;

            // Load users
            loadUsers();

            // Setup form handler
            document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
        });

        async function loadUsers() {
            try {
                document.getElementById('loadingOverlay').style.display = 'flex';
                
                const response = await apiClient.get('/users');
                const users = response.data;
                
                const tbody = document.getElementById('usersTableBody');
                tbody.innerHTML = '';
                
                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.first_name} ${user.last_name}</td>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td><span class="status-badge ${user.role}">${user.role}</span></td>
                        <td><span class="status-badge ${user.is_active ? 'success' : 'error'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
                        <td>${user.last_login ? formatUtils.datetime(user.last_login) : 'Never'}</td>
                        <td>
                            <button class="btn btn-sm btn-secondary" onclick="editUser(${user.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${user.id !== authManager.getCurrentUser().id ? `
                                <button class="btn btn-sm btn-danger" onclick="deactivateUser(${user.id})">
                                    <i class="fas fa-ban"></i>
                                </button>
                            ` : ''}
                        </td>
                    `;
                    tbody.appendChild(row);
                });
                
            } catch (error) {
                console.error('Failed to load users:', error);
                notificationUtils.error('Failed to load users');
            } finally {
                document.getElementById('loadingOverlay').style.display = 'none';
            }
        }

        function showCreateUserModal() {
            document.getElementById('createUserModal').style.display = 'flex';
        }

        function hideCreateUserModal() {
            document.getElementById('createUserModal').style.display = 'none';
            document.getElementById('createUserForm').reset();
        }

        async function handleCreateUser(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const userData = {
                firstName: formData.get('firstName'),
                lastName: formData.get('lastName'),
                username: formData.get('username'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                role: formData.get('role'),
                password: formData.get('password'),
                confirmPassword: formData.get('confirmPassword')
            };

            // Validate passwords match
            if (userData.password !== userData.confirmPassword) {
                notificationUtils.error('Passwords do not match');
                return;
            }

            try {
                await apiClient.post('/users', userData);
                notificationUtils.success('User created successfully');
                hideCreateUserModal();
                loadUsers();
            } catch (error) {
                console.error('Failed to create user:', error);
                notificationUtils.error(error.message || 'Failed to create user');
            }
        }

        function editUser(userId) {
            // Implement edit user functionality
            notificationUtils.info('Edit user functionality coming soon');
        }

        function deactivateUser(userId) {
            if (confirm('Are you sure you want to deactivate this user?')) {
                // Implement deactivate user functionality
                notificationUtils.info('Deactivate user functionality coming soon');
            }
        }
    </script>
</body>
</html>
