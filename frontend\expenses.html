<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Management - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item active">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>Expense Management</h1>
                <p>Track and manage business expenses</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreateExpenseModal()">
                    <i class="fas fa-plus"></i>
                    Add Expense
                </button>
            </div>
        </header>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search expenses..." onkeyup="filterExpenses()">
            </div>
            <div class="filter-controls">
                <select id="statusFilter" onchange="filterExpenses()">
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="paid">Paid</option>
                    <option value="rejected">Rejected</option>
                </select>
                <select id="categoryFilter" onchange="filterExpenses()">
                    <option value="all">All Categories</option>
                    <option value="Office Supplies">Office Supplies</option>
                    <option value="Marketing & Advertising">Marketing & Advertising</option>
                    <option value="Travel & Transportation">Travel & Transportation</option>
                    <option value="Utilities">Utilities</option>
                    <option value="Software & Technology">Software & Technology</option>
                    <option value="Professional Services">Professional Services</option>
                    <option value="Insurance">Insurance</option>
                    <option value="Rent & Facilities">Rent & Facilities</option>
                    <option value="Equipment & Maintenance">Equipment & Maintenance</option>
                    <option value="Training & Development">Training & Development</option>
                    <option value="Meals & Entertainment">Meals & Entertainment</option>
                    <option value="Bank Fees">Bank Fees</option>
                    <option value="Legal & Compliance">Legal & Compliance</option>
                    <option value="Other">Other</option>
                </select>
                <button class="btn btn-secondary" onclick="exportExpenses()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="table-container">
            <table class="data-table" id="expensesTable">
                <thead>
                    <tr>
                        <th>Expense #</th>
                        <th>Date</th>
                        <th>Category</th>
                        <th>Description</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Created By</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="expensesTableBody">
                    <!-- Expenses will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 0 of 0 expenses</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="changePage(-1)" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span class="page-numbers" id="pageNumbers"></span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="changePage(1)" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Create/Edit Expense Modal -->
    <div class="modal" id="expenseModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Expense</h3>
                <button class="modal-close" onclick="closeExpenseModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="expenseForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expenseDate">Expense Date *</label>
                            <input type="date" id="expenseDate" name="expense_date" required>
                        </div>
                        <div class="form-group">
                            <label for="category">Category *</label>
                            <select id="category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="Office Supplies">Office Supplies</option>
                                <option value="Marketing & Advertising">Marketing & Advertising</option>
                                <option value="Travel & Transportation">Travel & Transportation</option>
                                <option value="Utilities">Utilities</option>
                                <option value="Software & Technology">Software & Technology</option>
                                <option value="Professional Services">Professional Services</option>
                                <option value="Insurance">Insurance</option>
                                <option value="Rent & Facilities">Rent & Facilities</option>
                                <option value="Equipment & Maintenance">Equipment & Maintenance</option>
                                <option value="Training & Development">Training & Development</option>
                                <option value="Meals & Entertainment">Meals & Entertainment</option>
                                <option value="Bank Fees">Bank Fees</option>
                                <option value="Legal & Compliance">Legal & Compliance</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="3" required placeholder="Enter expense description"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="amount">Amount *</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0" required placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="taxAmount">Tax Amount</label>
                            <input type="number" id="taxAmount" name="tax_amount" step="0.01" min="0" value="0" placeholder="0.00">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="supplierId">Supplier (Optional)</label>
                        <select id="supplierId" name="supplier_id">
                            <option value="">Select Supplier</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="receipt">Receipt Upload</label>
                        <input type="file" id="receipt" name="receipt" accept="image/*,.pdf">
                        <small class="help-text">Upload receipt image or PDF (optional)</small>
                    </div>

                    <div class="expense-summary">
                        <div class="summary-row">
                            <span>Amount:</span>
                            <span id="expenseAmount">$0.00</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span id="expenseTax">$0.00</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span id="expenseTotal">$0.00</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeExpenseModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveExpense()">
                    <i class="fas fa-save"></i>
                    Save Expense
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/expenses.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Expenses page loading...');

            // Check authentication with delay to ensure auth manager is ready
            setTimeout(() => {
                if (!requireAuth()) {
                    console.log('❌ Authentication failed, stopping page initialization');
                    return;
                }

                console.log('✅ Authentication successful, initializing expenses page...');
                // Initialize expenses page
                initializeExpensesPage();
            }, 50);
        });
    </script>
</body>
</html>
