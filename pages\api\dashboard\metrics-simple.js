// Simplified dashboard metrics API
import jwt from 'jsonwebtoken';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key-for-development';
    
    try {
      const decoded = jwt.verify(token, jwtSecret);
      
      // Return demo metrics data
      const metrics = {
        totalRevenue: 125000,
        totalExpenses: 85000,
        netProfit: 40000,
        pendingInvoices: 12,
        overdueCount: 3,
        overdueAmount: 15000,
        revenueChange: 12.5,
        expensesChange: 8.2,
        profitChange: 15.3,
        period: {
          startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0]
        }
      };

      res.status(200).json({
        success: true,
        data: metrics
      });

    } catch (jwtError) {
      return res.status(401).json({ error: 'Invalid token' });
    }

  } catch (error) {
    console.error('Dashboard metrics error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
