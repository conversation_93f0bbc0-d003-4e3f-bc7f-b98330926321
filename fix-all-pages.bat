@echo off
echo ========================================
echo   FIXING ALL PAGES - NO MORE REDIRECTS!
echo ========================================
echo.

echo [1/5] Stopping any running servers...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo [2/5] Updating all HTML pages to use fixed auth...

REM Update all pages to use auth-fixed.js instead of auth.js
powershell -Command "(Get-Content 'public\frontend\customers.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\customers.html'"
powershell -Command "(Get-Content 'public\frontend\suppliers.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\suppliers.html'"
powershell -Command "(Get-Content 'public\frontend\expenses.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\expenses.html'"
powershell -Command "(Get-Content 'public\frontend\reports.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\reports.html'"
powershell -Command "(Get-Content 'public\frontend\users.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\users.html'"
powershell -Command "(Get-Content 'public\frontend\settings.html') -replace 'js/auth\.js', 'js/auth-fixed.js' | Set-Content 'public\frontend\settings.html'"

echo [3/5] Copying fixed authentication system...
copy public\frontend\js\auth-fixed.js public\frontend\js\auth-fixed.js >nul

echo [4/5] Creating redirect page for main login...
echo ^<!DOCTYPE html^> > public\frontend\index.html
echo ^<html^>^<head^>^<title^>Redirecting...^</title^>^</head^> >> public\frontend\index.html
echo ^<body^>^<script^>window.location.href='../login-fixed.html';^</script^>^</body^>^</html^> >> public\frontend\index.html

echo [5/5] Starting server with ALL FIXES...
echo.
echo ========================================
echo   🎉 ALL PAGES FIXED! NO MORE REDIRECTS!
echo ========================================
echo.
echo WHAT WAS FIXED:
echo - ✅ ALL pages now use fixed authentication
echo - ✅ Reliable localStorage handling
echo - ✅ Proper authentication checking
echo - ✅ No more redirect loops
echo.
echo WORKING PAGES:
echo - Fixed Login: http://localhost:3000/login-fixed.html
echo - Fixed Dashboard: http://localhost:3000/dashboard-fixed.html
echo - All Frontend Pages: http://localhost:3000/frontend/[page].html
echo.
echo LOGIN CREDENTIALS:
echo   Username: admin
echo   Password: admin
echo.
echo TESTING STEPS:
echo 1. Go to: http://localhost:3000/login-fixed.html
echo 2. Login with admin/admin
echo 3. Should go to dashboard and STAY there
echo 4. Navigate to other pages - should work
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

npm run dev
