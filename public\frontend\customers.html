<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Management - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item active">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>Customer Management</h1>
                <p>Manage your customer database and relationships</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreateCustomerModal()">
                    <i class="fas fa-plus"></i>
                    Add Customer
                </button>
            </div>
        </header>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search customers..." onkeyup="filterCustomers()">
            </div>
            <div class="filter-controls">
                <select id="statusFilter" onchange="filterCustomers()">
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <button class="btn btn-secondary" onclick="exportCustomers()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Customers Table -->
        <div class="table-container">
            <table class="data-table" id="customersTable">
                <thead>
                    <tr>
                        <th>Customer Code</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Total Billed</th>
                        <th>Total Paid</th>
                        <th>Outstanding</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="customersTableBody">
                    <!-- Customers will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 0 of 0 customers</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="changePage(-1)" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span class="page-numbers" id="pageNumbers"></span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="changePage(1)" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Create/Edit Customer Modal -->
    <div class="modal" id="customerModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Customer</h3>
                <button class="modal-close" onclick="closeCustomerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName">Customer Name *</label>
                            <input type="text" id="customerName" name="name" required placeholder="Enter customer name">
                        </div>
                        <div class="form-group">
                            <label for="customerEmail">Email</label>
                            <input type="email" id="customerEmail" name="email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerPhone">Phone</label>
                            <input type="tel" id="customerPhone" name="phone" placeholder="+****************">
                        </div>
                        <div class="form-group">
                            <label for="taxNumber">Tax Number</label>
                            <input type="text" id="taxNumber" name="tax_number" placeholder="Tax ID or VAT number">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="customerAddress">Address</label>
                        <textarea id="customerAddress" name="address" rows="3" placeholder="Street address"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerCity">City</label>
                            <input type="text" id="customerCity" name="city" placeholder="City">
                        </div>
                        <div class="form-group">
                            <label for="customerCountry">Country</label>
                            <input type="text" id="customerCountry" name="country" placeholder="Country">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="creditLimit">Credit Limit</label>
                            <input type="number" id="creditLimit" name="credit_limit" step="0.01" min="0" value="0" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="paymentTerms">Payment Terms (Days)</label>
                            <input type="number" id="paymentTerms" name="payment_terms" min="1" value="30" placeholder="30">
                        </div>
                    </div>

                    <div class="form-group" id="statusGroup" style="display: none;">
                        <label for="customerStatus">Status</label>
                        <select id="customerStatus" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                    <i class="fas fa-save"></i>
                    Save Customer
                </button>
            </div>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div class="modal" id="customerDetailsModal" style="display: none;">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="detailsModalTitle">Customer Details</h3>
                <button class="modal-close" onclick="closeCustomerDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="customer-details-content">
                    <!-- Customer details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCustomerDetailsModal()">Close</button>
                <button type="button" class="btn btn-primary" onclick="editCustomerFromDetails()">
                    <i class="fas fa-edit"></i>
                    Edit Customer
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth-fixed.js"></script>
    <script src="js/customers.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!requireAuth()) return;

            // Initialize customers page
            initializeCustomersPage();
        });
    </script>
</body>
</html>
