// Expense Management JavaScript
class ExpenseManager {
    constructor() {
        this.expenses = [];
        this.suppliers = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.currentExpense = null;
    }

    // Initialize expenses page
    async initialize() {
        try {
            this.showLoading();
            await this.loadSuppliers();
            await this.loadExpenses();
            this.setupEventListeners();
            this.hideLoading();
        } catch (error) {
            console.error('Failed to initialize expenses page:', error);
            this.hideLoading();
            this.showAlert('Failed to load expenses data', 'error');
        }
    }

    // Load suppliers for dropdown
    async loadSuppliers() {
        try {
            const response = await apiClient.request('/suppliers?limit=1000');
            this.suppliers = response.data.suppliers;
            this.populateSupplierDropdown();
        } catch (error) {
            console.error('Failed to load suppliers:', error);
        }
    }

    // Populate supplier dropdown
    populateSupplierDropdown() {
        const supplierSelect = document.getElementById('supplierId');
        supplierSelect.innerHTML = '<option value="">Select Supplier</option>';
        
        this.suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = `${supplier.name} (${supplier.supplier_code})`;
            supplierSelect.appendChild(option);
        });
    }

    // Load expenses
    async loadExpenses() {
        try {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const categoryFilter = document.getElementById('categoryFilter').value;

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: searchTerm,
                status: statusFilter,
                category: categoryFilter
            });

            const response = await apiClient.request(`/expenses?${params}`);
            this.expenses = response.data.expenses;
            this.updatePagination(response.data.pagination);
            this.renderExpensesTable();
        } catch (error) {
            console.error('Failed to load expenses:', error);
            this.showAlert('Failed to load expenses', 'error');
        }
    }

    // Render expenses table
    renderExpensesTable() {
        const tbody = document.getElementById('expensesTableBody');
        tbody.innerHTML = '';

        if (this.expenses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">No expenses found</td>
                </tr>
            `;
            return;
        }

        this.expenses.forEach(expense => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <a href="#" onclick="viewExpense(${expense.id})" class="expense-link">
                        ${expense.expense_number}
                    </a>
                </td>
                <td>${formatUtils.date(expense.expense_date)}</td>
                <td>${expense.category}</td>
                <td>${expense.description}</td>
                <td>${formatUtils.currency(expense.total_amount)}</td>
                <td>
                    <span class="status-badge status-${expense.status}">
                        ${expense.status.charAt(0).toUpperCase() + expense.status.slice(1)}
                    </span>
                </td>
                <td>${expense.created_by_name || 'Unknown'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-secondary" onclick="editExpense(${expense.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${expense.status === 'pending' ? `
                            <button class="btn btn-sm btn-success" onclick="approveExpense(${expense.id})" title="Approve">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="rejectExpense(${expense.id})" title="Reject">
                                <i class="fas fa-times"></i>
                            </button>
                        ` : ''}
                        ${expense.receipt_path ? `
                            <button class="btn btn-sm btn-info" onclick="viewReceipt(${expense.id})" title="View Receipt">
                                <i class="fas fa-file"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-sm btn-danger" onclick="deleteExpense(${expense.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // Update pagination
    updatePagination(pagination) {
        this.currentPage = pagination.page;
        this.totalPages = pagination.pages;

        // Update pagination info
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        document.getElementById('paginationInfo').textContent = 
            `Showing ${start}-${end} of ${pagination.total} expenses`;

        // Update pagination controls
        document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
        document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;

        // Update page numbers
        this.renderPageNumbers();
    }

    // Render page numbers
    renderPageNumbers() {
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';

        const maxVisible = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisible - 1);

        if (endPage - startPage + 1 < maxVisible) {
            startPage = Math.max(1, endPage - maxVisible + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === this.currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }

    // Go to specific page
    async goToPage(page) {
        this.currentPage = page;
        await this.loadExpenses();
    }

    // Change page (next/previous)
    async changePage(direction) {
        const newPage = this.currentPage + direction;
        if (newPage >= 1 && newPage <= this.totalPages) {
            await this.goToPage(newPage);
        }
    }

    // Show create expense modal
    showCreateExpenseModal() {
        this.currentExpense = null;
        document.getElementById('modalTitle').textContent = 'Add New Expense';
        document.getElementById('expenseForm').reset();
        
        // Set default date
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('expenseDate').value = today;
        
        this.updateExpenseSummary();
        document.getElementById('expenseModal').style.display = 'flex';
    }

    // Close expense modal
    closeExpenseModal() {
        document.getElementById('expenseModal').style.display = 'none';
        this.currentExpense = null;
    }

    // Update expense summary
    updateExpenseSummary() {
        const amount = parseFloat(document.getElementById('amount').value) || 0;
        const taxAmount = parseFloat(document.getElementById('taxAmount').value) || 0;
        const total = amount + taxAmount;
        
        document.getElementById('expenseAmount').textContent = formatUtils.currency(amount);
        document.getElementById('expenseTax').textContent = formatUtils.currency(taxAmount);
        document.getElementById('expenseTotal').textContent = formatUtils.currency(total);
    }

    // Save expense
    async saveExpense() {
        try {
            this.showLoading();
            
            // Collect form data
            const formData = new FormData(document.getElementById('expenseForm'));
            const expenseData = Object.fromEntries(formData.entries());
            
            // Validate required fields
            if (!expenseData.expense_date || !expenseData.category || !expenseData.description || !expenseData.amount) {
                throw new Error('Please fill in all required fields');
            }

            // Convert numeric fields
            expenseData.amount = parseFloat(expenseData.amount) || 0;
            expenseData.tax_amount = parseFloat(expenseData.tax_amount) || 0;
            
            if (expenseData.amount <= 0) {
                throw new Error('Amount must be greater than 0');
            }
            
            // Save expense
            const endpoint = this.currentExpense ? `/expenses/${this.currentExpense.id}` : '/expenses';
            const method = this.currentExpense ? 'PUT' : 'POST';
            
            const response = await apiClient.request(endpoint, {
                method,
                body: JSON.stringify(expenseData)
            });
            
            this.hideLoading();
            this.showAlert('Expense saved successfully!', 'success');
            this.closeExpenseModal();
            await this.loadExpenses();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(error.message || 'Failed to save expense', 'error');
        }
    }

    // Approve expense
    async approveExpense(expenseId) {
        if (!confirm('Are you sure you want to approve this expense?')) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/expenses/${expenseId}/approve`, { method: 'POST' });
            
            this.hideLoading();
            this.showAlert('Expense approved successfully!', 'success');
            await this.loadExpenses();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to approve expense', 'error');
        }
    }

    // Reject expense
    async rejectExpense(expenseId) {
        const reason = prompt('Please enter rejection reason:');
        if (!reason) return;

        try {
            this.showLoading();
            await apiClient.request(`/expenses/${expenseId}/reject`, {
                method: 'POST',
                body: JSON.stringify({ reason })
            });
            
            this.hideLoading();
            this.showAlert('Expense rejected successfully!', 'success');
            await this.loadExpenses();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to reject expense', 'error');
        }
    }

    // Delete expense
    async deleteExpense(expenseId) {
        if (!confirm('Are you sure you want to delete this expense?')) {
            return;
        }

        try {
            this.showLoading();
            await apiClient.request(`/expenses/${expenseId}`, { method: 'DELETE' });
            
            this.hideLoading();
            this.showAlert('Expense deleted successfully!', 'success');
            await this.loadExpenses();
            
        } catch (error) {
            this.hideLoading();
            this.showAlert('Failed to delete expense', 'error');
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Search input
        document.getElementById('searchInput').addEventListener('input', 
            debounce(() => this.loadExpenses(), 300));
        
        // Amount and tax calculation
        document.getElementById('amount').addEventListener('input', () => this.updateExpenseSummary());
        document.getElementById('taxAmount').addEventListener('input', () => this.updateExpenseSummary());
    }

    // Show loading
    showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    // Hide loading
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // Show alert
    showAlert(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Global expense manager instance
const expenseManager = new ExpenseManager();

// Global functions for HTML onclick handlers
function initializeExpensesPage() {
    expenseManager.initialize();
}

function showCreateExpenseModal() {
    expenseManager.showCreateExpenseModal();
}

function closeExpenseModal() {
    expenseManager.closeExpenseModal();
}

function saveExpense() {
    expenseManager.saveExpense();
}

function filterExpenses() {
    expenseManager.currentPage = 1;
    expenseManager.loadExpenses();
}

function changePage(direction) {
    expenseManager.changePage(direction);
}

function editExpense(id) {
    console.log('Edit expense:', id);
}

function viewExpense(id) {
    console.log('View expense:', id);
}

function approveExpense(id) {
    expenseManager.approveExpense(id);
}

function rejectExpense(id) {
    expenseManager.rejectExpense(id);
}

function deleteExpense(id) {
    expenseManager.deleteExpense(id);
}

function viewReceipt(id) {
    console.log('View receipt for expense:', id);
}

function exportExpenses() {
    console.log('Export expenses');
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
