<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <ul class="menu-list">
                <li class="menu-item active">
                    <a href="dashboard.html" class="menu-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="invoices.html" class="menu-link">
                        <i class="fas fa-file-invoice"></i>
                        <span>Invoices</span>
                        <span class="badge">12</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="expenses.html" class="menu-link">
                        <i class="fas fa-receipt"></i>
                        <span>Expenses</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="payroll.html" class="menu-link">
                        <i class="fas fa-users"></i>
                        <span>Payroll</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="reports.html" class="menu-link">
                        <i class="fas fa-chart-bar"></i>
                        <span>Reports</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu('accounting')">
                        <i class="fas fa-book"></i>
                        <span>Accounting</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu" id="accounting-submenu">
                        <li><a href="chart-of-accounts.html">Chart of Accounts</a></li>
                        <li><a href="general-ledger.html">General Ledger</a></li>
                        <li><a href="bank-reconciliation.html">Bank Reconciliation</a></li>
                    </ul>
                </li>
                <li class="menu-item">
                    <a href="#" class="menu-link" onclick="toggleSubmenu('contacts')">
                        <i class="fas fa-address-book"></i>
                        <span>Contacts</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </a>
                    <ul class="submenu" id="contacts-submenu">
                        <li><a href="customers.html">Customers</a></li>
                        <li><a href="suppliers.html">Suppliers</a></li>
                        <li><a href="employees.html">Employees</a></li>
                    </ul>
                </li>
                <li class="menu-item admin-only">
                    <a href="users.html" class="menu-link">
                        <i class="fas fa-users-cog"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="menu-item admin-only">
                    <a href="settings.html" class="menu-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="menu-item admin-only">
                    <a href="audit-trail.html" class="menu-link">
                        <i class="fas fa-search"></i>
                        <span>Audit Trail</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="userName">Loading...</span>
                    <span class="user-role" id="userRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="authManager.logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Dashboard</h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="header-btn" id="notificationBtn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="header-btn" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu">
                        <button class="user-menu-btn" id="userMenuBtn">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                            <a href="settings.html"><i class="fas fa-cog"></i> Settings</a>
                            <hr>
                            <a href="#" onclick="authManager.logout()"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Role-based Access Information -->
            <div class="access-info-banner" id="accessInfoBanner" style="display: none;">
                <div class="access-info-content">
                    <div class="access-info-text">
                        <i class="fas fa-info-circle"></i>
                        <span id="accessInfoMessage">Your access level and permissions</span>
                    </div>
                    <button class="access-info-close" onclick="hideAccessInfo()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Key Metrics Cards -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-icon success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="metric-info">
                        <h3 class="metric-value" id="totalRevenue">$0</h3>
                        <p class="metric-label">Total Revenue</p>
                        <span class="metric-change positive">+12.5%</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-icon warning">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="metric-info">
                        <h3 class="metric-value" id="totalExpenses">$0</h3>
                        <p class="metric-label">Total Expenses</p>
                        <span class="metric-change negative">+8.2%</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-icon info">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-info">
                        <h3 class="metric-value" id="netProfit">$0</h3>
                        <p class="metric-label">Net Profit</p>
                        <span class="metric-change positive">+15.3%</span>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-icon primary">
                        <i class="fas fa-file-invoice"></i>
                    </div>
                    <div class="metric-info">
                        <h3 class="metric-value" id="pendingInvoices">0</h3>
                        <p class="metric-label">Pending Invoices</p>
                        <span class="metric-change neutral">-</span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Revenue vs Expenses</h3>
                        <div class="chart-controls">
                            <select id="chartPeriod">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                    </div>
                    <canvas id="revenueExpenseChart"></canvas>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Expense Categories</h3>
                    </div>
                    <canvas id="expenseCategoryChart"></canvas>
                </div>
            </div>

            <!-- Recent Activity & Quick Actions -->
            <div class="activity-section">
                <div class="recent-activity">
                    <div class="section-header">
                        <h3>Recent Activity</h3>
                        <a href="audit-trail.html" class="view-all-link">View All</a>
                    </div>
                    <div class="activity-list" id="activityList">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Invoice #INV-001 created for $2,500</p>
                                <span class="activity-time">2 hours ago</span>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Expense of $150 added for office supplies</p>
                                <span class="activity-time">4 hours ago</span>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <i class="fas fa-money-bill"></i>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Payment received for Invoice #INV-002</p>
                                <span class="activity-time">1 day ago</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <div class="section-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn" onclick="window.location.href='invoices.html?action=create'">
                            <i class="fas fa-plus"></i>
                            <span>Create Invoice</span>
                        </button>
                        <button class="action-btn" onclick="window.location.href='expenses.html?action=create'">
                            <i class="fas fa-receipt"></i>
                            <span>Add Expense</span>
                        </button>
                        <button class="action-btn admin-only" onclick="window.location.href='customers.html?action=create'">
                            <i class="fas fa-user-plus"></i>
                            <span>Add Customer</span>
                        </button>
                        <button class="action-btn admin-only" onclick="window.location.href='users.html'">
                            <i class="fas fa-users-cog"></i>
                            <span>Manage Users</span>
                        </button>
                        <button class="action-btn" onclick="window.location.href='reports.html'">
                            <i class="fas fa-chart-bar"></i>
                            <span>Generate Report</span>
                        </button>
                        <button class="action-btn admin-only" onclick="window.location.href='settings.html'">
                            <i class="fas fa-cog"></i>
                            <span>System Settings</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Alerts & Notifications -->
            <div class="alerts-section" id="alertsSection">
                <div class="section-header">
                    <h3>Alerts & Notifications</h3>
                </div>
                <div class="alerts-list" id="alertsList">
                    <!-- Alerts will be loaded dynamically -->
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading dashboard data...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Dashboard loading...');

            // Check authentication with delay to ensure auth manager is ready
            setTimeout(() => {
                if (!requireAuth()) {
                    console.log('❌ Authentication failed, stopping dashboard initialization');
                    return;
                }

                console.log('✅ Authentication successful, initializing dashboard...');
                // Initialize dashboard components
                initializeDashboard();
                loadDashboardData();
                initializeCharts();
                setupEventListeners();
            }, 50);
        });

        // Global functions for HTML onclick handlers
        function toggleSubmenu(menuId) {
            const submenu = document.getElementById(`${menuId}-submenu`);
            const menuItem = submenu.closest('.menu-item');

            if (submenu && menuItem) {
                submenu.classList.toggle('expanded');
                menuItem.classList.toggle('expanded');
            }
        }

        function hideAccessInfo() {
            const banner = document.getElementById('accessInfoBanner');
            if (banner) {
                banner.style.display = 'none';
            }
        }
    </script>
</body>
</html>
