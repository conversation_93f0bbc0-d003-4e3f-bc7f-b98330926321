<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Enterprise Accounting</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .logo p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .demo-credentials {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-size: 14px;
        }

        .demo-credentials h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🏢 Enterprise Accounting</h1>
            <p>Secure Business Management System</p>
        </div>

        <div id="alert" class="alert"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="credential">Username or Email</label>
                <input type="text" id="credential" name="credential" value="admin" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="admin" required>
            </div>

            <button type="submit" class="btn" id="loginBtn">
                Sign In
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Signing in...</p>
            </div>
        </form>

        <div class="demo-credentials">
            <h4>🔑 Demo Credentials:</h4>
            <p><strong>Admin:</strong> admin / admin</p>
            <p><strong>Manager:</strong> manager / manager</p>
            <p><strong>Accountant:</strong> accountant / accountant</p>
        </div>
    </div>

    <script src="frontend/js/auth-fixed.js"></script>
    <script>
        const loginForm = document.getElementById('loginForm');
        const alertDiv = document.getElementById('alert');
        const loadingDiv = document.getElementById('loading');
        const loginBtn = document.getElementById('loginBtn');

        function showAlert(message, type) {
            alertDiv.textContent = message;
            alertDiv.className = `alert ${type}`;
            alertDiv.style.display = 'block';
        }

        function hideAlert() {
            alertDiv.style.display = 'none';
        }

        function showLoading() {
            loadingDiv.style.display = 'block';
            loginBtn.disabled = true;
        }

        function hideLoading() {
            loadingDiv.style.display = 'none';
            loginBtn.disabled = false;
        }

        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const credential = document.getElementById('credential').value.trim();
            const password = document.getElementById('password').value;

            if (!credential || !password) {
                showAlert('Please enter both username and password', 'error');
                return;
            }

            try {
                hideAlert();
                showLoading();

                console.log('🔐 Starting login process...');
                const result = await fixedAuthManager.login(credential, password);

                if (result.success) {
                    showAlert('Login successful! Redirecting...', 'success');
                    
                    setTimeout(() => {
                        console.log('🚀 Redirecting to dashboard...');
                        window.location.href = 'dashboard-fixed.html';
                    }, 1000);
                }
            } catch (error) {
                console.error('❌ Login failed:', error);
                showAlert(error.message || 'Login failed. Please try again.', 'error');
            } finally {
                hideLoading();
            }
        });

        // Check if already logged in
        if (fixedAuthManager.isAuthenticated()) {
            console.log('✅ Already logged in, redirecting...');
            window.location.href = 'dashboard-fixed.html';
        }
    </script>
</body>
</html>
