// Dashboard JavaScript Module
class DashboardManager {
    constructor() {
        this.charts = {};
        this.dashboardData = null;
        this.refreshInterval = null;
    }

    // Initialize dashboard
    async initialize() {
        try {
            this.showLoading();
            await this.loadUserInfo();
            await this.loadDashboardData();
            this.initializeCharts();
            this.setupEventListeners();
            this.startAutoRefresh();
            this.hideLoading();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            this.hideLoading();
            this.showError('Failed to load dashboard data');
        }
    }

    // Show loading overlay
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.style.display = 'flex';
    }

    // Hide loading overlay
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) overlay.style.display = 'none';
    }

    // Show error message
    showError(message) {
        // You can implement a toast notification system here
        console.error(message);
    }

    // Load user information and apply role-based restrictions
    async loadUserInfo() {
        const user = authManager.getCurrentUser();
        if (user) {
            document.getElementById('userName').textContent = `${user.firstName} ${user.lastName}`;
            document.getElementById('userRole').textContent = user.role;

            // Apply role-based UI restrictions
            this.applyRoleBasedRestrictions(user.role);
        }
    }

    // Apply role-based restrictions to UI elements
    applyRoleBasedRestrictions(userRole) {
        // Hide admin-only menu items for non-admin users
        if (userRole !== 'admin') {
            // Hide user management and settings for non-admin users
            const restrictedMenuItems = [
                'settings.html',
                'users.html',
                'audit-trail.html'
            ];

            restrictedMenuItems.forEach(href => {
                const menuItem = document.querySelector(`a[href="${href}"]`);
                if (menuItem) {
                    menuItem.closest('.menu-item').style.display = 'none';
                }
            });

            // Hide admin-specific quick actions
            const adminActions = document.querySelectorAll('.admin-only');
            adminActions.forEach(action => {
                action.style.display = 'none';
            });

            // Show role-specific limitations message
            this.showRoleLimitations(userRole);

            // Show access info banner for non-admin users
            if (userRole !== 'admin') {
                this.showAccessInfoBanner(userRole);
            }
        }

        // Apply specific restrictions based on role
        switch (userRole) {
            case 'employee':
                this.applyEmployeeRestrictions();
                break;
            case 'auditor':
                this.applyAuditorRestrictions();
                break;
            case 'accountant':
                this.applyAccountantRestrictions();
                break;
            case 'manager':
                this.applyManagerRestrictions();
                break;
        }
    }

    // Show role-specific limitations
    showRoleLimitations(userRole) {
        const limitations = {
            employee: 'You have limited access. You can only view your own expenses and payroll information.',
            auditor: 'You have read-only access to financial data for auditing purposes.',
            accountant: 'You can manage invoices and expenses but cannot approve expenses or access user management.',
            manager: 'You can approve expenses and view reports but cannot manage users or system settings.'
        };

        if (limitations[userRole]) {
            const alertsSection = document.getElementById('alertsSection');
            if (alertsSection) {
                const limitationAlert = document.createElement('div');
                limitationAlert.className = 'alert-item info';
                limitationAlert.innerHTML = `
                    <div class="alert-content">
                        <h4 class="alert-title">Access Limitations</h4>
                        <p class="alert-message">${limitations[userRole]}</p>
                    </div>
                `;
                alertsSection.querySelector('.alerts-list').appendChild(limitationAlert);
            }
        }
    }

    // Apply employee-specific restrictions
    applyEmployeeRestrictions() {
        // Hide most dashboard metrics except personal ones
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach((card, index) => {
            if (index > 0) { // Keep only the first metric card
                card.style.display = 'none';
            }
        });

        // Hide charts section
        const chartsSection = document.querySelector('.charts-section');
        if (chartsSection) chartsSection.style.display = 'none';

        // Modify quick actions to show only employee-relevant actions
        const actionButtons = document.querySelector('.action-buttons');
        if (actionButtons) {
            actionButtons.innerHTML = `
                <button class="action-btn" onclick="window.location.href='expenses.html?action=create'">
                    <i class="fas fa-receipt"></i>
                    <span>Submit Expense</span>
                </button>
                <button class="action-btn" onclick="window.location.href='payroll.html'">
                    <i class="fas fa-money-bill"></i>
                    <span>View Payroll</span>
                </button>
            `;
        }
    }

    // Apply auditor-specific restrictions
    applyAuditorRestrictions() {
        // Remove all action buttons since auditors have read-only access
        const actionButtons = document.querySelector('.action-buttons');
        if (actionButtons) {
            actionButtons.innerHTML = `
                <button class="action-btn" onclick="window.location.href='reports.html'">
                    <i class="fas fa-chart-bar"></i>
                    <span>View Reports</span>
                </button>
                <button class="action-btn" onclick="window.location.href='audit-trail.html'">
                    <i class="fas fa-search"></i>
                    <span>Audit Trail</span>
                </button>
            `;
        }

        // Add read-only indicator to all forms
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.setAttribute('readonly', true);
            element.style.backgroundColor = '#f3f4f6';
        });
    }

    // Apply accountant-specific restrictions
    applyAccountantRestrictions() {
        // Hide expense approval actions
        const approvalButtons = document.querySelectorAll('.approve-btn');
        approvalButtons.forEach(btn => btn.style.display = 'none');
    }

    // Apply manager-specific restrictions
    applyManagerRestrictions() {
        // Managers have most access except user management and settings
        // Restrictions are mainly applied through menu hiding
    }

    // Show access info banner for non-admin users
    showAccessInfoBanner(userRole) {
        const banner = document.getElementById('accessInfoBanner');
        const message = document.getElementById('accessInfoMessage');

        if (!banner || !message) return;

        const messages = {
            employee: '⚠️ Limited Access: You can only view your own expenses and payroll information. Contact your administrator for additional access.',
            auditor: '👁️ Read-Only Access: You have view-only access to financial data for auditing purposes. You cannot modify any records.',
            accountant: '📊 Accounting Access: You can manage invoices and expenses but cannot approve expenses or access user management.',
            manager: '👔 Management Access: You can approve expenses and view reports but cannot manage users or system settings.'
        };

        if (messages[userRole]) {
            message.textContent = messages[userRole];
            banner.style.display = 'block';
        }
    }

    // Load dashboard data
    async loadDashboardData() {
        try {
            // Simulate API calls - replace with actual API endpoints
            const [metrics, recentActivity, alerts] = await Promise.all([
                this.loadMetrics(),
                this.loadRecentActivity(),
                this.loadAlerts()
            ]);

            this.dashboardData = {
                metrics,
                recentActivity,
                alerts
            };

            this.updateMetricsDisplay();
            this.updateRecentActivity();
            this.updateAlerts();

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            throw error;
        }
    }

    // Load metrics data
    async loadMetrics() {
        try {
            const response = await apiClient.request('/dashboard/metrics-simple');
            return response.data;
        } catch (error) {
            console.error('Failed to load metrics:', error);
            // Fallback to demo data
            return {
                totalRevenue: 125000,
                totalExpenses: 85000,
                netProfit: 40000,
                pendingInvoices: 12,
                revenueChange: 12.5,
                expensesChange: 8.2,
                profitChange: 15.3
            };
        }
    }

    // Load recent activity
    async loadRecentActivity() {
        // Simulate API call - replace with actual endpoint
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    {
                        id: 1,
                        type: 'invoice',
                        description: 'Invoice #INV-001 created for $2,500',
                        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                        icon: 'fas fa-file-invoice'
                    },
                    {
                        id: 2,
                        type: 'expense',
                        description: 'Expense of $150 added for office supplies',
                        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                        icon: 'fas fa-receipt'
                    },
                    {
                        id: 3,
                        type: 'payment',
                        description: 'Payment received for Invoice #INV-002',
                        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
                        icon: 'fas fa-money-bill'
                    }
                ]);
            }, 800);
        });
    }

    // Load alerts
    async loadAlerts() {
        // Simulate API call - replace with actual endpoint
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    {
                        id: 1,
                        type: 'warning',
                        title: 'Overdue Invoices',
                        message: '3 invoices are overdue and require attention',
                        timestamp: new Date()
                    },
                    {
                        id: 2,
                        type: 'info',
                        title: 'Monthly Report Ready',
                        message: 'Your monthly financial report is ready for review',
                        timestamp: new Date()
                    }
                ]);
            }, 600);
        });
    }

    // Update metrics display
    updateMetricsDisplay() {
        if (!this.dashboardData?.metrics) return;

        const { metrics } = this.dashboardData;

        // Update metric values
        document.getElementById('totalRevenue').textContent = this.formatCurrency(metrics.totalRevenue);
        document.getElementById('totalExpenses').textContent = this.formatCurrency(metrics.totalExpenses);
        document.getElementById('netProfit').textContent = this.formatCurrency(metrics.netProfit);
        document.getElementById('pendingInvoices').textContent = metrics.pendingInvoices;

        // Update change indicators
        this.updateChangeIndicator('totalRevenue', metrics.revenueChange);
        this.updateChangeIndicator('totalExpenses', metrics.expensesChange);
        this.updateChangeIndicator('netProfit', metrics.profitChange);
    }

    // Update change indicator
    updateChangeIndicator(metricId, change) {
        const metricCard = document.getElementById(metricId)?.closest('.metric-card');
        if (!metricCard) return;

        const changeElement = metricCard.querySelector('.metric-change');
        if (!changeElement) return;

        changeElement.textContent = `${change > 0 ? '+' : ''}${change}%`;
        changeElement.className = 'metric-change';
        
        if (change > 0) {
            changeElement.classList.add('positive');
        } else if (change < 0) {
            changeElement.classList.add('negative');
        } else {
            changeElement.classList.add('neutral');
        }
    }

    // Update recent activity
    updateRecentActivity() {
        if (!this.dashboardData?.recentActivity) return;

        const activityList = document.getElementById('activityList');
        if (!activityList) return;

        activityList.innerHTML = '';

        this.dashboardData.recentActivity.forEach(activity => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            activityItem.innerHTML = `
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <p class="activity-text">${activity.description}</p>
                    <span class="activity-time">${this.formatTimeAgo(activity.timestamp)}</span>
                </div>
            `;
            activityList.appendChild(activityItem);
        });
    }

    // Update alerts
    updateAlerts() {
        if (!this.dashboardData?.alerts) return;

        const alertsList = document.getElementById('alertsList');
        if (!alertsList) return;

        alertsList.innerHTML = '';

        this.dashboardData.alerts.forEach(alert => {
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item ${alert.type}`;
            alertItem.innerHTML = `
                <div class="alert-content">
                    <h4 class="alert-title">${alert.title}</h4>
                    <p class="alert-message">${alert.message}</p>
                    <span class="alert-time">${this.formatTimeAgo(alert.timestamp)}</span>
                </div>
                <button class="alert-dismiss" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;
            alertsList.appendChild(alertItem);
        });
    }

    // Initialize charts
    initializeCharts() {
        this.initializeRevenueExpenseChart();
        this.initializeExpenseCategoryChart();
    }

    // Initialize revenue vs expense chart
    initializeRevenueExpenseChart() {
        const ctx = document.getElementById('revenueExpenseChart');
        if (!ctx) return;

        // Sample data - replace with actual data
        const data = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
                {
                    label: 'Revenue',
                    data: [12000, 15000, 18000, 14000, 20000, 22000],
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Expenses',
                    data: [8000, 9000, 11000, 10000, 13000, 15000],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4
                }
            ]
        };

        this.charts.revenueExpense = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // Initialize expense category chart
    initializeExpenseCategoryChart() {
        const ctx = document.getElementById('expenseCategoryChart');
        if (!ctx) return;

        // Sample data - replace with actual data
        const data = {
            labels: ['Office Supplies', 'Marketing', 'Travel', 'Utilities', 'Software', 'Other'],
            datasets: [{
                data: [3000, 5000, 2000, 1500, 4000, 2500],
                backgroundColor: [
                    '#2563eb',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6',
                    '#64748b'
                ]
            }]
        };

        this.charts.expenseCategory = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }

    // Setup event listeners
    setupEventListeners() {
        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const sidebar = document.getElementById('sidebar');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
            });
        }

        if (mobileMenuToggle) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-visible');
            });
        }

        // User menu toggle
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');

        if (userMenuBtn && userDropdown) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('show');
            });

            document.addEventListener('click', () => {
                userDropdown.classList.remove('show');
            });
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Chart period change
        const chartPeriod = document.getElementById('chartPeriod');
        if (chartPeriod) {
            chartPeriod.addEventListener('change', (e) => {
                this.updateChartPeriod(e.target.value);
            });
        }
    }

    // Toggle theme
    toggleTheme() {
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('i');

        if (body.getAttribute('data-theme') === 'dark') {
            body.removeAttribute('data-theme');
            icon.className = 'fas fa-moon';
            localStorage.setItem('theme', 'light');
        } else {
            body.setAttribute('data-theme', 'dark');
            icon.className = 'fas fa-sun';
            localStorage.setItem('theme', 'dark');
        }
    }

    // Update chart period
    updateChartPeriod(period) {
        // Implement chart data update based on period
        console.log('Updating chart period to:', period);
        // This would typically involve fetching new data and updating the chart
    }

    // Start auto refresh
    startAutoRefresh() {
        // Refresh dashboard data every 5 minutes
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    }

    // Stop auto refresh
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // Utility functions
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    formatTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        return `${Math.floor(diffInSeconds / 86400)} days ago`;
    }
}

// Global dashboard manager instance
const dashboardManager = new DashboardManager();

// Global functions for HTML onclick handlers
function initializeDashboard() {
    dashboardManager.initialize();
}

function loadDashboardData() {
    dashboardManager.loadDashboardData();
}

function initializeCharts() {
    dashboardManager.initializeCharts();
}

function setupEventListeners() {
    dashboardManager.setupEventListeners();
}

function toggleSubmenu(menuId) {
    const submenu = document.getElementById(`${menuId}-submenu`);
    const menuItem = submenu.closest('.menu-item');
    
    if (submenu && menuItem) {
        submenu.classList.toggle('expanded');
        menuItem.classList.toggle('expanded');
    }
}

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.querySelector('i').className = 'fas fa-sun';
        }
    }
});
