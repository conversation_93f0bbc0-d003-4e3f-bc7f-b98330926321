<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supplier Management - Enterprise Accounting</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="dashboard-page">
    <!-- Sidebar Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-calculator"></i>
                <span>Enterprise Accounting</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="sidebar-menu">
            <div class="menu-item">
                <a href="dashboard.html">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="invoices.html">
                    <i class="fas fa-file-invoice"></i>
                    <span>Invoices</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="expenses.html">
                    <i class="fas fa-receipt"></i>
                    <span>Expenses</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="customers.html">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
            </div>

            <div class="menu-item active">
                <a href="suppliers.html">
                    <i class="fas fa-truck"></i>
                    <span>Suppliers</span>
                </a>
            </div>

            <div class="menu-item">
                <a href="reports.html">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="users.html">
                    <i class="fas fa-users-cog"></i>
                    <span>User Management</span>
                </a>
            </div>

            <div class="menu-item admin-only">
                <a href="settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>

        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name" id="sidebarUserName">Loading...</span>
                    <span class="user-role" id="sidebarUserRole">Loading...</span>
                </div>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1>Supplier Management</h1>
                <p>Manage your vendor and supplier relationships</p>
            </div>
            <div class="header-right">
                <button class="btn btn-primary" onclick="showCreateSupplierModal()">
                    <i class="fas fa-plus"></i>
                    Add Supplier
                </button>
            </div>
        </header>

        <!-- Filters and Search -->
        <div class="filters-section">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Search suppliers..." onkeyup="filterSuppliers()">
            </div>
            <div class="filter-controls">
                <select id="statusFilter" onchange="filterSuppliers()">
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                <button class="btn btn-secondary" onclick="exportSuppliers()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Suppliers Table -->
        <div class="table-container">
            <table class="data-table" id="suppliersTable">
                <thead>
                    <tr>
                        <th>Supplier Code</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Total Expenses</th>
                        <th>Total Spent</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="suppliersTableBody">
                    <!-- Suppliers will be loaded here -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container">
            <div class="pagination-info">
                <span id="paginationInfo">Showing 0 of 0 suppliers</span>
            </div>
            <div class="pagination-controls">
                <button class="btn btn-secondary" id="prevPageBtn" onclick="changePage(-1)" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <span class="page-numbers" id="pageNumbers"></span>
                <button class="btn btn-secondary" id="nextPageBtn" onclick="changePage(1)" disabled>
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Create/Edit Supplier Modal -->
    <div class="modal" id="supplierModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Add New Supplier</h3>
                <button class="modal-close" onclick="closeSupplierModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="supplierForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplierName">Supplier Name *</label>
                            <input type="text" id="supplierName" name="name" required placeholder="Enter supplier name">
                        </div>
                        <div class="form-group">
                            <label for="supplierEmail">Email</label>
                            <input type="email" id="supplierEmail" name="email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplierPhone">Phone</label>
                            <input type="tel" id="supplierPhone" name="phone" placeholder="+****************">
                        </div>
                        <div class="form-group">
                            <label for="taxNumber">Tax Number</label>
                            <input type="text" id="taxNumber" name="tax_number" placeholder="Tax ID or VAT number">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="supplierAddress">Address</label>
                        <textarea id="supplierAddress" name="address" rows="3" placeholder="Street address"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="supplierCity">City</label>
                            <input type="text" id="supplierCity" name="city" placeholder="City">
                        </div>
                        <div class="form-group">
                            <label for="supplierCountry">Country</label>
                            <input type="text" id="supplierCountry" name="country" placeholder="Country">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="paymentTerms">Payment Terms (Days)</label>
                        <input type="number" id="paymentTerms" name="payment_terms" min="1" value="30" placeholder="30">
                    </div>

                    <div class="form-group" id="statusGroup" style="display: none;">
                        <label for="supplierStatus">Status</label>
                        <select id="supplierStatus" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeSupplierModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveSupplier()">
                    <i class="fas fa-save"></i>
                    Save Supplier
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth-fixed.js"></script>
    <script src="js/suppliers.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!requireAuth()) return;

            // Initialize suppliers page
            initializeSuppliersPage();
        });
    </script>
</body>
</html>
