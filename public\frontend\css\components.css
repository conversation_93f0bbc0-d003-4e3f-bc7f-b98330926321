/* Reusable UI Components */

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.chart-container {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.chart-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.chart-controls select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  background-color: var(--white);
}

.chart-container canvas {
  max-height: 300px;
}

/* Activity Section */
.activity-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.recent-activity,
.quick-actions {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.section-header h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.view-all-link {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.activity-item:hover {
  background-color: var(--gray-50);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  color: var(--gray-900);
  font-size: var(--font-size-sm);
  margin: 0 0 var(--spacing-1) 0;
  line-height: 1.4;
}

.activity-time {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

/* Quick Actions */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.action-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

.action-btn i {
  font-size: var(--font-size-lg);
}

/* Alerts Section */
.alerts-section {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  border-left: 4px solid;
}

.alert-item.warning {
  background-color: #fef3c7;
  border-left-color: var(--warning-color);
}

.alert-item.info {
  background-color: #dbeafe;
  border-left-color: var(--info-color);
}

.alert-item.error {
  background-color: #fee2e2;
  border-left-color: var(--error-color);
}

.alert-item.success {
  background-color: #d1fae5;
  border-left-color: var(--success-color);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--gray-900);
  margin: 0 0 var(--spacing-1) 0;
}

.alert-message {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  margin: 0 0 var(--spacing-1) 0;
  line-height: 1.4;
}

.alert-time {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

.alert-dismiss {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--gray-400);
  padding: var(--spacing-1);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.alert-dismiss:hover {
  color: var(--gray-600);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-overlay .loading-spinner {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-8);
  text-align: center;
  box-shadow: var(--shadow-xl);
}

/* Notification System */
.notification-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out;
}

.notification-success {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.notification-error {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.notification-warning {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.notification-info {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.notification-message {
  flex: 1;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-1);
  color: inherit;
  opacity: 0.7;
  border-radius: var(--radius-sm);
  transition: opacity var(--transition-fast);
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Data Tables */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.data-table th,
.data-table td {
  padding: var(--spacing-3) var(--spacing-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.data-table th {
  background-color: var(--gray-50);
  font-weight: 600;
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}

.data-table td {
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.data-table tr:hover {
  background-color: var(--gray-50);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: var(--font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.success {
  background-color: #d1fae5;
  color: #065f46;
}

.status-badge.warning {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.error {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-badge.info {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge.neutral {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: var(--spacing-4);
}

.modal-content {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  margin: 0;
  color: var(--gray-900);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.modal-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--gray-400);
  padding: var(--spacing-2);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

.modal-body {
  padding: var(--spacing-6);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

/* Button Sizes */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

/* Additional Status Badge Styles */
.status-active {
  background-color: #d1fae5;
  color: #065f46;
}

.status-inactive {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-draft {
  background-color: #f3f4f6;
  color: #374151;
}

.status-sent {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-paid {
  background-color: #d1fae5;
  color: #065f46;
}

.status-overdue {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* Table Container */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.page-btn:hover {
  background-color: #f9fafb;
}

.page-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Filters Section */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-box input {
  width: 100%;
  padding: 10px 10px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-controls select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

/* Form Styles */
.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Modal Large Size */
.modal-content.large {
  max-width: 900px;
}

/* Report Styles */
.report-selection-section {
  margin-bottom: 30px;
}

.report-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.report-type-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.report-type-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-type-card.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.report-icon {
  font-size: 32px;
  color: #3b82f6;
  margin-bottom: 10px;
}

.report-type-card h3 {
  margin: 10px 0 5px;
  color: #1f2937;
}

.report-type-card p {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

/* Settings Styles */
.settings-tabs {
  display: flex;
  border-bottom: 2px solid #e5e7eb;
  margin-bottom: 30px;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-button:hover {
  color: #374151;
  background-color: #f9fafb;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background-color: #eff6ff;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.settings-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 20px;
}

.settings-card h3 {
  margin: 0 0 25px;
  color: #1f2937;
  font-size: 20px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 10px;
}

.settings-card h4 {
  margin: 20px 0 15px;
  color: #374151;
  font-size: 16px;
}

.notification-section {
  margin-bottom: 25px;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 6px;
}

.notification-section h4 {
  margin: 0 0 15px;
  color: #1f2937;
}

.backup-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.backup-section h4 {
  margin: 0 0 10px;
  color: #1f2937;
}

.backup-section p {
  color: #6b7280;
  margin-bottom: 15px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
  margin-top: 5px;
}

/* Additional responsive styles */
@media (max-width: 768px) {
  .settings-tabs {
    flex-direction: column;
  }

  .tab-button {
    justify-content: center;
    border-bottom: none;
    border-right: 3px solid transparent;
  }

  .tab-button.active {
    border-bottom: none;
    border-right-color: #3b82f6;
  }
}
  min-height: auto;
  width: auto;
}

.btn-danger {
  background-color: var(--error-color);
  color: var(--white);
}

.btn-danger:hover {
  background-color: #dc2626;
}

/* Table Improvements */
.table-container {
  overflow-x: auto;
}

.data-table th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Role-specific status badges */
.status-badge.admin {
  background-color: #d1fae5;
  color: #065f46;
}

.status-badge.manager {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-badge.accountant {
  background-color: #fef3c7;
  color: #92400e;
}

.status-badge.auditor {
  background-color: #ede9fe;
  color: #5b21b6;
}

.status-badge.employee {
  background-color: #f3f4f6;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .charts-section,
  .activity-section {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chart-container,
  .recent-activity,
  .quick-actions,
  .alerts-section {
    padding: var(--spacing-4);
  }

  .notification-container {
    left: var(--spacing-4);
    right: var(--spacing-4);
    max-width: none;
  }

  .data-table {
    font-size: var(--font-size-xs);
  }

  .data-table th,
  .data-table td {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .modal-content {
    margin: var(--spacing-2);
    max-width: none;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--spacing-4);
  }

  .modal-footer {
    flex-direction: column;
  }
}
